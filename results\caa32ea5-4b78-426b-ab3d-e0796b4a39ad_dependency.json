{"dependencies": {"dependencies": [{"source_column": "trade_date", "target_column": "settlement_date", "relationship": "T+2", "confidence_score": 1.0, "domain": "Finance", "risk_note": "Assumed T+2 settlement based on industry practice. Confirm if T+1, T+3, or same-day settlement applies.", "formula": "settlement_date = trade_date + 2 days"}, {"source_column": "price", "target_column": "total_amount", "relationship": "price * quantity", "confidence_score": 1.0, "domain": "Generic", "risk_note": null, "formula": "total_amount = price * quantity"}, {"source_column": "country", "target_column": "currency", "relationship": "IF-THEN", "confidence_score": 1.0, "domain": "Generic", "risk_note": "Assumed currency dependency based on country (e.g., country='IN' ⇒ currency='INR'). Confirm mappings.", "formula": "CASE WHEN country = 'IN' THEN 'INR' WHEN country = 'US' THEN 'USD' ... END"}], "derived_columns": [{"column_name": "total_amount", "formula": "price * quantity", "dependencies": ["price", "quantity"], "confidence_score": 1.0, "domain": "Generic", "risk_note": null}], "business_rules": [{"rule": "IF country='IN' THEN currency='INR'", "dependencies": ["country", "currency"], "confidence_score": 1.0, "domain": "Generic", "risk_note": "Assumed currency dependency. Confirm mappings."}, {"rule": "discharge_date > admission_date", "dependencies": ["discharge_date", "admission_date"], "confidence_score": 1.0, "domain": "Healthcare", "risk_note": "Assumed logical discharge after admission. Confirm exceptions."}], "generation_order": ["trade_date", "price", "quantity", "total_amount", "settlement_date", "country", "currency", "admission_date", "discharge_date"]}}