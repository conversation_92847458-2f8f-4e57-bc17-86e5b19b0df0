# DataEcho Technology Context

**Last Updated: June 25, 2025**

## Core Technology Stack

### Runtime Environment
- **Python Version**: 3.11+ (Required for modern async support and performance)
- **Package Manager**: pip with requirements.txt
- **Virtual Environment**: Docker-based development (recommended) or local venv

### Web Framework & API
- **Primary Framework**: FastAPI 0.104+ 
  - Async/await native support
  - Automatic OpenAPI/Swagger documentation
  - Pydantic integration for request/response validation
  - WebSocket support for real-time updates
- **ASGI Server**: Uvicorn for development, Gunicorn + Uvicorn for production
- **API Design**: RESTful with OpenAPI 3.0 specification

### AI & LLM Integration
- **Primary LLM Service**: OpenRouter API
  - Model: deepseek-prover-v2:free (cost-effective during development)
  - Backup: OpenAI GPT models (when OpenRouter unavailable)
- **Agent Framework**: LangGraph (replacing basic LangChain)
  - Current: Basic MemorySaver for conversation context
  - Planned: Full StateGraph implementation for multi-agent coordination
- **LLM Libraries**:
  - `langchain-openai`: OpenAI-compatible API integration
  - `langchain-core`: Core LangChain abstractions
  - `langgraph`: State graph orchestration (MVP2)

### Data Processing & Analysis
- **Data Manipulation**: pandas 2.1.4+ for CSV processing and statistical analysis
- **Data Validation**: Pydantic v2 for request/response models and data validation
- **File Handling**: 
  - `aiofiles`: Async file operations
  - `python-multipart`: Multipart form data handling for file uploads

### Development & Deployment
- **Containerization**: Docker + Docker Compose
  - Development environment consistency
  - Easy deployment and scaling
  - Volume persistence for uploads, sessions, and results
- **Environment Management**: python-dotenv for configuration
- **Process Management**: uvicorn with reload for development

## Development Environment Setup

### Required Dependencies (requirements.txt)
```
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pandas>=2.1.4
pydantic>=2.0.0
langchain-openai>=0.1.0
langchain-core>=0.1.0
langgraph>=0.1.0
python-multipart>=0.0.6
aiofiles>=23.0.0
python-dotenv>=1.0.0
```

### Environment Variables (.env)
```
# LLM API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENAI_API_KEY=your_openai_api_key_here  # Optional backup

# Application Configuration
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO

# File Storage Configuration
UPLOAD_DIR=./uploads
SESSION_DIR=./sessions
RESULTS_DIR=./results
MAX_FILE_SIZE=100MB

# LLM Configuration
DEFAULT_MODEL=deepseek-prover-v2:free
FALLBACK_MODEL=gpt-3.5-turbo
MAX_TOKENS=4000
TEMPERATURE=0.7
```

### Docker Configuration

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# Create required directories
RUN mkdir -p uploads sessions results

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### docker-compose.yaml
```yaml
version: '3.8'

services:
  dataecho:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./sessions:/app/sessions
      - ./results:/app/results
      - ./.env:/app/.env
    environment:
      - ENVIRONMENT=development
    restart: unless-stopped
```

## Project Structure

### Current Implementation (MVP1)
```
dataecho/
├── main.py                    # FastAPI application entry point
├── app/
│   ├── __init__.py
│   ├── agents/               # AI agent implementations
│   │   ├── __init__.py
│   │   ├── base_agent.py    # Abstract base class for all agents
│   │   ├── profiler_agent.py    # Data profiling and analysis
│   │   ├── dependency_agent.py  # Relationship mapping
│   │   └── generator_agent.py   # Synthetic data generation
│   ├── orchestrator/        # Workflow orchestration
│   │   ├── __init__.py
│   │   └── workflow_orchestrator.py
│   ├── services/            # Business logic services
│   │   ├── __init__.py
│   │   ├── session_manager.py
│   │   └── llm_service.py
│   ├── models/              # Pydantic data models
│   │   ├── __init__.py
│   │   ├── requests.py      # API request models
│   │   ├── responses.py     # API response models
│   │   └── agents.py        # Agent-specific models
│   ├── storage/             # File storage management
│   │   ├── __init__.py
│   │   └── storage_manager.py
│   ├── websocket/           # Real-time communication
│   │   ├── __init__.py
│   │   └── connection_manager.py
│   ├── utils/               # Utility functions
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   └── data_utils.py
│   ├── config.py            # Application configuration
│   └── prompts.py           # LLM prompts and templates
├── uploads/                 # User-uploaded CSV files (Docker volume)
├── sessions/                # Session data storage (Docker volume)
├── results/                 # Generated results (Docker volume)
├── memory-bank/             # Memory bank files (this setup)
├── requirements.txt         # Python dependencies
├── Dockerfile              # Container configuration
├── docker-compose.yaml     # Docker orchestration
├── .env                    # Environment variables (create manually)
├── .gitignore              # Git ignore patterns
└── .windsurfrules          # Workspace-specific rules
```

## Development Workflow

### Local Development Setup
1. **Clone Repository**: `git clone <repository-url>`
2. **Environment Setup**: Create `.env` file with required API keys
3. **Docker Development**: `docker-compose up --build`
4. **Local Python Alternative**: 
   ```bash
   python -m venv venv
   source venv/bin/activate  # or venv\Scripts\activate on Windows
   pip install -r requirements.txt
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

### API Access Points
- **Main API**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs (Swagger UI)
- **Alternative Docs**: http://localhost:8000/redoc (ReDoc)
- **OpenAPI Spec**: http://localhost:8000/openapi.json

### Development Tools Integration
- **IDE**: VS Code recommended with Python and Docker extensions
- **API Testing**: Built-in FastAPI docs or Postman/Thunder Client
- **Debugging**: Python debugger integration with uvicorn --reload
- **Logging**: Structured logging with configurable levels

## Technical Constraints & Considerations

### Performance Constraints
- **File Size Limits**: 100MB max file upload (configurable)
- **Processing Memory**: Large CSV files processed in chunks
- **Concurrent Sessions**: Limited by available memory and LLM API rate limits
- **Generation Time**: Target <15 minutes for 50K records

### LLM API Constraints
- **Rate Limits**: OpenRouter free tier has usage limitations
- **Context Windows**: 4K tokens for DeepSeek, manage context carefully
- **Cost Management**: Monitor token usage in production
- **Fallback Strategy**: OpenAI backup when OpenRouter unavailable

### Security Considerations
- **API Keys**: Never commit to repository, use environment variables
- **File Uploads**: Validate file types and scan for malicious content
- **Data Retention**: Automatic cleanup of session data after configurable period
- **Input Validation**: Comprehensive validation of all user inputs

### Scalability Considerations
- **Horizontal Scaling**: FastAPI supports multiple worker processes
- **Database Migration**: Plan for PostgreSQL/MongoDB in MVP2/3
- **Caching Strategy**: Redis for session caching in production
- **Load Balancing**: Nginx reverse proxy for production deployment

## External Dependencies & APIs

### Primary Dependencies
- **OpenRouter API**: Primary LLM service with multiple model access
- **OpenAI API**: Fallback LLM service for reliability
- **No Database**: File-based persistence for MVP1 simplicity

### Planned Integrations (MVP2/3)
- **MongoDB**: Document storage for session and result persistence
- **Redis**: Caching layer for performance optimization
- **Tavily/Serper**: Web search APIs for domain enrichment
- **Vector Database**: Embeddings storage for RAG functionality

### API Rate Limits & Quotas
- **OpenRouter Free Tier**: Limited requests per minute
- **OpenAI**: Usage-based billing with configurable limits
- **File Storage**: Local filesystem with automatic cleanup
- **WebSocket Connections**: Limited by server resources

## Monitoring & Observability

### Current Implementation
- **Basic Logging**: Python logging with configurable levels
- **Health Checks**: Simple endpoint status monitoring
- **Error Tracking**: Basic exception handling and logging

### Planned Enhancements (MVP2/3)
- **Structured Logging**: JSON logging with correlation IDs
- **Metrics Collection**: Prometheus metrics for performance monitoring
- **Distributed Tracing**: Request tracing across agent workflows
- **Error Monitoring**: Sentry integration for error aggregation

## Migration Path & Technical Debt

### Known Technical Debt
- **File-Based Persistence**: Replace with proper database in MVP2
- **Basic LangGraph Usage**: Expand to full state graph orchestration
- **Limited Error Handling**: Enhance with comprehensive retry logic
- **No Authentication**: Implement proper auth/authz for production

### Migration Strategy
1. **MVP1 → MVP2**: Introduce database persistence, enhanced LangGraph
2. **MVP2 → MVP3**: Add RAG system, advanced UI, additional agents
3. **MVP3 → Production**: Add authentication, monitoring, scalability features

---
*This technical context provides the foundation for all development decisions and should be updated as the technology stack evolves.*