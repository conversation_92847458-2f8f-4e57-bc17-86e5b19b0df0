# DataEcho Workspace-Specific Memory Bank Rules
# Multi-Agent Synthetic Data Generation Platform
# Publicis Sapient Internal Product

## Project Identity
- **Project Name**: DataEcho
- **Type**: AI-Powered Synthetic Data Generation Platform
- **Architecture**: Multi-Agent System with FastAPI Orchestration
- **Current Status**: MVP1 Functional, MVP2 Planning Phase
- **Technology Stack**: Python 3.11, FastAPI, LangGraph, DeepSeek via OpenRouter
- **Business Context**: Publicis Sapient internal product development

## Memory Bank Initialization Commands
- "initialize memory bank" - Create complete DataEcho memory bank structure
- "update memory bank" or "UMB" - Update existing memory bank files
- "rebuild context" - Re-read all memory bank files for full context
- "memory status" - Report current memory bank state and DataEcho development status
- "agent status" - Report current agent implementation status and planned agents

## DataEcho-Specific Development Patterns

### Multi-Agent Architecture Patterns
- **Current Implementation**: Sequential 3-agent pipeline (ProfilerAgent, DependencyAgent, GeneratorAgent)
- **Planned Evolution**: LangGraph-orchestrated parallel multi-agent system
- **Agent Communication**: File-based results storage, session state management
- **Orchestration**: WorkflowOrchestrator class manages agent sequence

### FastAPI Development Standards
- **API Structure**: RESTful endpoints with WebSocket support for real-time updates
- **Session Management**: JSON-based session persistence in `sessions/` directory
- **File Handling**: Upload storage in `uploads/`, results in `results/`
- **Error Handling**: Comprehensive error responses with detailed logging

### LangGraph Integration Approach
- **Current**: Basic MemorySaver for conversation context
- **Planned**: Full state graph implementation for agent coordination
- **State Management**: Complex workflow state tracking and recovery
- **Parallel Processing**: Coordinated agent execution capabilities

### Data Generation Workflow Patterns
- **Input Processing**: CSV file analysis and statistical profiling
- **Dependency Analysis**: Relationship mapping and constraint identification
- **Synthetic Generation**: Batch processing with configurable parameters
- **Quality Assurance**: Validation and consistency checking

## Development Environment Configuration
- **Python Version**: 3.11+
- **API Framework**: FastAPI with uvicorn
- **LLM Integration**: OpenRouter for DeepSeek model access
- **Development Setup**: Docker Compose for containerized development
- **File Storage**: Local directories with Docker volume persistence

## Agent Development Guidelines
- **Base Class**: Inherit from BaseAgent for consistent interface
- **LLM Integration**: Use LLMService wrapper for model interactions
- **Error Handling**: Implement retry logic and graceful failure modes
- **Result Format**: JSON output with structured data validation
- **Progress Tracking**: WebSocket updates for real-time status

## Business Context Awareness
- **Market Positioning**: First multi-agent architecture in synthetic data space
- **Competitive Advantage**: Natural language interface, real-time domain enrichment
- **Target Market**: Enterprise clients requiring privacy-compliant test data
- **Success Metrics**: Data quality, generation speed, domain intelligence

## MVP Development Priorities
- **MVP1 (Completed)**: Basic 3-agent pipeline with CSV processing
- **MVP2 (Planning)**: LangGraph integration, enhanced agent coordination
- **MVP3 (Future)**: RAG system, domain intelligence, advanced UI

## Quality Standards
- **Code Quality**: Follow Python best practices, type hints, comprehensive error handling
- **Documentation**: Maintain API documentation, agent specifications, architecture decisions
- **Testing**: Implement unit tests for agents, integration tests for workflows
- **Performance**: Optimize for large dataset processing, memory management

## External Integration Points
- **OpenRouter API**: DeepSeek model access for agent LLM capabilities
- **Future Integrations**: Tavily/Serper for web search, MongoDB for persistence
- **Domain Sources**: Industry standards, regulatory compliance data
- **Export Formats**: CSV, JSON, API integrations for downstream systems