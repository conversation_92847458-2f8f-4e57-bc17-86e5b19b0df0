{"dependencies": {"derived_columns": {"Table_1": {"current_balance": {"formula": "previous_balance + SUM(Transaction_Amount)", "scope": {"table": "Table_2", "join_key": "account_ID"}, "conditions": [{"Transaction_Date": {"<=": "report_date"}}], "risk_note": "Assumed to be previous_balance + sum of transactions. Check with domain experts."}}}, "business_rules": {"Table_2": [{"if": {"Account_Type": "SAVINGS"}, "then": {"Transaction_Amount": {">=": 0}}, "risk_note": "Overdrafts may not be allowed for SAVINGS accounts."}]}, "column_dependencies": {"Table_1": {"current_balance": ["previous_balance", "Transaction_Amount"]}, "Table_2": {"account_ID": {"foreign_table": "Table_1", "column": "account_ID", "required": true}}}, "generation_order": ["Table_1:account_ID", "Table_1:previous_balance", "Table_1:report_date", "Table_1:effective_date", "Table_1:expired_date", "Table_2:account_ID", "Table_2:Transaction_Date", "Table_2:Transaction_Amount", "Table_2:Frequency", "Table_2:Account_Type", "Table_1:current_balance"]}}