{"dependencies": {"columns": {"trade_date": {"depends_on": [], "derived": false, "formula": null, "risk_note": null, "confidence_score": 1.0}, "settlement_date": {"depends_on": ["trade_date"], "derived": true, "formula": "trade_date + 2", "risk_note": "Assumed T+2 settlement rule. Confirm with domain expert.", "confidence_score": 0.7}, "price": {"depends_on": [], "derived": false, "formula": null, "risk_note": null, "confidence_score": 1.0}, "quantity": {"depends_on": [], "derived": false, "formula": null, "risk_note": null, "confidence_score": 1.0}, "total_amount": {"depends_on": ["price", "quantity"], "derived": true, "formula": "price * quantity", "risk_note": null, "confidence_score": 0.9}, "country": {"depends_on": [], "derived": false, "formula": null, "risk_note": null, "confidence_score": 1.0}, "currency": {"depends_on": ["country"], "derived": true, "formula": "IF country = 'IN' THEN 'INR' ELSE 'USD'", "risk_note": "Assumes currencies for countries. Extend to cover other countries.", "confidence_score": 0.8}}, "generation_order": ["trade_date", "price", "quantity", "country", "settlement_date", "total_amount", "currency"]}}