{"profile": {"columns": [{"name": "trade_id", "type": "id", "semantic": "Unique identifier for each trade", "constraints": ["not null", "unique"], "depends_on": [], "derived_using": null, "example_values": ["T1001", "T1002", "T1015"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "increment_with_mask", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming 'T' prefix with an incremental number.", "justification": "The field is prefixed with 'T' and followed by an incremental number, indicating a unique identifier for each trade."}, {"name": "portfolio_id", "type": "id", "semantic": "Unique identifier for the portfolio associated with the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["P100", "P101", "P104"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["P100"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming standardized portfolio IDs exist beyond the sample.", "justification": "The field has a 'P' prefix followed by numbers indicating different portfolios, with some repetition."}, {"name": "trader_id", "type": "categorical", "semantic": "Unique identifier for the trader who executed the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["TR200", "TR201", "TR204"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["TR200", "TR201"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming trader IDs are standardized but reused across trades.", "justification": "The field has a 'TR' prefix with numbers, indicating different traders with some repetition."}, {"name": "counterparty", "type": "categorical", "semantic": "The financial institution or entity on the other side of the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Goldman Sachs", "<PERSON>", "Barclays"], "full_domain": ["Goldman Sachs", "<PERSON>", "Morgan Stanley", "Barclays", "Credit Suisse", "HSBC", "BNP Paribas"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["HSBC", "BNP Paribas"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming full domain is consistent with the sample.", "justification": "Field contains major financial institutions that would typically act as counterparties in trades."}, {"name": "instrument_type", "type": "categorical", "semantic": "Type of financial instrument traded (e.g., Equity, Bond, FX)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Equity", "Bond", "FX"], "full_domain": ["Equity", "Bond", "FX", "Derivative"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["Equity"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming full domain is consistent with the sample.", "justification": "The values are standard instrument types in financial trading."}, {"name": "instrument_id", "type": "text", "semantic": "Unique identifier for the traded instrument (e.g., ticker symbol or ISIN)", "constraints": ["not null"], "depends_on": ["instrument_type"], "derived_using": null, "example_values": ["AAPL", "US10Y", "EUR/USD"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": ["AMZN", "GBP/USD"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.9, "risk_notes": "Instrument IDs are varied and possibly have specific meanings for each instrument type.", "justification": "The field contains standard instrument identifiers like ticker symbols, bond codes, and FX pairs."}, {"name": "trade_date", "type": "date", "semantic": "The date the trade was executed", "constraints": ["not null", "must follow YYYY-MM-DD", "must be ≤ settlement_date"], "depends_on": ["settlement_date"], "derived_using": null, "example_values": ["2023-08-15", "2023-08-16", "2023-08-29"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample_with_constraints", "temporal_pattern": "increasing", "profiling_confidence": 1.0, "risk_notes": "Assuming trade_date must be <= settlement_date in all cases.", "justification": "Date follows 'YYYY-MM-DD' format and increases sequentially. Also, in all rows, settlement_date is on or after trade_date."}, {"name": "settlement_date", "type": "date", "semantic": "The date the trade is settled (i.e., final transfer of assets)", "constraints": ["not null", "must follow YYYY-MM-DD", "must be ≥ trade_date"], "depends_on": [], "derived_using": null, "example_values": ["2023-08-17", "2023-08-19", "2023-08-31"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample_with_constraints", "temporal_pattern": "increasing", "profiling_confidence": 1.0, "risk_notes": "Assuming settlement_date has a business day offset relative to trade_date.", "justification": "Dates follow 'YYYY-MM-DD' format and increases sequentially. Also, in all rows, settlement_date is on or after trade_date."}, {"name": "quantity", "type": "numeric", "semantic": "Number of units (shares, bonds, etc.) traded", "constraints": ["not null", "must be > 0"], "depends_on": ["instrument_type"], "derived_using": null, "example_values": [100, 150, 750], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming quantities are generally positive integers, with some variation based on instrument type.", "justification": "Quantities are positive integers and seem to vary per instrument type (e.g., FX trades might have much higher quantities than equity)."}, {"name": "price", "type": "numeric", "semantic": "Price per unit of the traded instrument (specific to the instrument type)", "constraints": ["not null", "must be > 0"], "depends_on": ["instrument_type"], "derived_using": null, "example_values": [175.5, 99.25, 2725.0], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Prices will vary depending on instrument type, and must also match the typical price range of the instrument type.", "justification": "Prices are positive values and vary significantly depending on instrument type (e.g., lower for bonds, higher for FX, etc.)"}, {"name": "currency", "type": "categorical", "semantic": "Currency of the trade (ISO code, e.g., USD, EUR, GBP)", "constraints": ["not null"], "depends_on": ["instrument_type", "instrument_id"], "derived_using": null, "example_values": ["USD", "EUR", "GBP"], "full_domain": ["USD", "EUR", "GBP"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["USD", "GBP"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming full domain is consistent with the sample.", "justification": "Values cover major currencies and are appropriate based on the instrument type and ID (more USD for US markets, GBP for LSE, etc.)."}, {"name": "fx_rate_to_usd", "type": "numeric", "semantic": "Exchange rate to USD (if not in USD)", "constraints": ["not null", "must be > 0"], "depends_on": ["currency"], "derived_using": null, "example_values": [1.0, 1.1, 1.3], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [1.0, 1.3], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming non-USD currencies have >1 rates to USD.", "justification": "Determined based on whether currency is USD (in which case =1.0) or another currency (implies an FX rate)."}, {"name": "trade_value", "type": "numeric", "semantic": "Total value of the trade in its original currency (quantity × price)", "constraints": ["not null", "must be > 0"], "depends_on": ["quantity", "price"], "derived_using": "trade_value = quantity × price", "example_values": [17550.0, 19850.0, 44507.5], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "derived_immediately", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Derived directly from quantity and price, no further assumptions.", "justification": "Verifying with sample shows that trade_value = quantity × price."}, {"name": "usd_equivalent_value", "type": "numeric", "semantic": "Transaction value converted to USD (trade_value × fx_rate_to_usd if not in USD; or trade_value if in USD)", "constraints": ["not null", "must be > 0"], "depends_on": ["trade_value", "fx_rate_to_usd"], "derived_using": "usd_equivalent_value = trade_value × fx_rate_to_usd", "example_values": [17550.0, 121000.0, 62010.0], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "derived_immediately", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Derived directly from trade_value and fx_rate_to_usd, no further assumptions.", "justification": "Verifying with sample shows that usd_equivalent_value = trade_value × fx_rate_to_usd (correctly handling USD trades as well)."}, {"name": "buy_sell", "type": "categorical", "semantic": "Direction of the trade (Buy or Sell)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Buy", "<PERSON>ll"], "full_domain": ["Buy", "<PERSON>ll"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Buy", "<PERSON>ll"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming buy and sell are the only options.", "justification": "Domain is clearly 'Buy' and 'Sell' based on sample."}, {"name": "venue", "type": "categorical", "semantic": "Exchange or platform where the trade was executed", "constraints": ["not null"], "depends_on": ["instrument_type"], "derived_using": null, "example_values": ["NASDAQ", "OTC", "LSE"], "full_domain": ["NASDAQ", "OTC", "EBS", "CME", "LSE", "NYSE"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["LSE", "NYSE"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Not all instrument types are traded on all venues.", "justification": "Values are standard trading venues, and venue is likely related to instrument_type (e.g., 'EQUITY' traded on NASDAQ)."}, {"name": "trade_type", "type": "categorical", "semantic": "Type of trade execution (Market or Limit)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Market", "Limit"], "full_domain": ["Market", "Limit"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Market", "Limit"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming no other trade types are available in the system.", "justification": "Domain is clearly 'Market' and 'Limit' based on sample."}, {"name": "status", "type": "categorical", "semantic": "Current status of the trade (Executed, Pending, etc.)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Executed", "Pending"], "full_domain": ["Executed", "Pending", "Rejected"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Executed"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.8, "risk_notes": "Other statuses may exist beyond 'Executed', 'Pending', and 'Rejected'.", "justification": "Values are standard trade statuses, but other possible statuses exist not shown in the sample."}, {"name": "risk_score", "type": "numeric", "semantic": "Risk score associated with the trade (higher is riskier)", "constraints": ["not null", "must be ≥ 1", "must be ≤ 5"], "depends_on": [], "derived_using": null, "example_values": [3, 4, 1], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [3, 2, 5], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming a predefined scale of 1 to 5 for risk scoring.", "justification": "Sam<PERSON> shows risk scores between 1 and 5."}, {"name": "is_margin_trade", "type": "boolean", "semantic": "Whether the trade was made using margin", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": [false, true], "full_domain": [false, true], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [false], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming margin trades are less common and flagged as Boolean.", "justification": "Field is Boolean with 'True' indicating margin trade."}, {"name": "is_compliant", "type": "boolean", "semantic": "Whether the trade is compliant with regulations", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": [true, false], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [true], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Assuming compliance is assessed per trade and not automatically derived.", "justification": "Field is Boolean with both values present in the sample, compliant being more common."}, {"name": "compliance_notes", "type": "text", "semantic": "Notes or reasons if trade does not comply with regulations", "constraints": [], "depends_on": ["is_compliant"], "derived_using": null, "example_values": ["High leverage", "Exceeds risk threshold", null], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [null, "High leverage", "Exceeds risk threshold"], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.9, "risk_notes": "Assuming notes are only present when is_compliant = False.", "justification": "Notes appear to be present only when is_compliant = False (though not all non-compliant trades have notes)."}, {"name": "approval_status", "type": "categorical", "semantic": "Status of the trade's approval (e.g., Approved/Rejected/Pending)", "constraints": ["not null"], "depends_on": ["is_compliant"], "derived_using": null, "example_values": ["Approved", "Rejected", "Pending"], "full_domain": ["Approved", "Rejected", "Pending"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Approved"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "Full domain is assumed based on the sample.", "justification": "Values are logical for an approval status field and are consistent with is_compliant dependencies (non-compliant trades are mostly rejected or pending)."}], "global_rules": ["IF currency = 'USD' THEN fx_rate_to_usd = 1.0", "IF currency != 'USD' THEN fx_rate_to_usd != 1.0", "IF is_compliant = false THEN compliance_notes IS NOT NULL", "IF is_compliant = true THEN compliance_notes IS NULL", "IF venue = 'LSE' THEN currency = 'GBP'", "IF instrument_id = 'AMZN' THEN instrument_type = 'Equity'", "IF instrument_id = 'GBP/USD' THEN instrument_type = 'FX'"]}, "dataset_info": {"row_count": 15, "column_count": 23, "columns": ["trade_id", "portfolio_id", "trader_id", "counterparty", "instrument_type", "instrument_id", "trade_date", "settlement_date", "quantity", "price", "currency", "fx_rate_to_usd", "trade_value", "usd_equivalent_value", "buy_sell", "venue", "trade_type", "status", "risk_score", "is_margin_trade", "is_compliant", "compliance_notes", "approval_status"]}}