{"dependencies": {"dependencies": [{"type": "derivation", "target_column": "total_price", "formula": "price * quantity", "source_columns": ["price", "quantity"], "domain": "Retail/E-commerce", "confidence_score": 1.0, "risk_note": null}, {"type": "derivation", "target_column": "settlement_date", "formula": "trade_date + 2_business_days", "source_columns": ["trade_date"], "domain": "Finance/Trading", "confidence_score": 0.95, "risk_note": "Assumes standard T+2 settlement. Weekends/holidays might shift by more than 2 calendar days."}, {"type": "derivation", "target_column": "age_at_admission", "formula": "YEAR(admission_date) - YEAR(date_of_birth)", "source_columns": ["admission_date", "date_of_birth"], "domain": "Healthcare", "confidence_score": 0.9, "risk_note": "Simple year-based calculation. More precise age would consider month/day. Ignores leap years."}, {"type": "derivation", "target_column": "tax_amount", "formula": "total_price * tax_rate", "source_columns": ["total_price", "tax_rate"], "domain": "Retail/E-commerce/Finance", "confidence_score": 0.95, "risk_note": "Assumes tax_rate is a flat percentage. Real-world tax can be complex (e.g., progressive, location-based)."}], "business_rules": [{"type": "conditional", "condition": "country == 'US'", "consequence": "currency == 'USD'", "source_columns": ["country"], "target_column": "currency", "domain": "General/International Business", "confidence_score": 0.98, "risk_note": null}, {"type": "conditional", "condition": "country == 'IN'", "consequence": "currency == 'INR'", "source_columns": ["country"], "target_column": "currency", "domain": "General/International Business", "confidence_score": 0.98, "risk_note": null}, {"type": "conditional", "condition": "credit_score < 600", "consequence": "loan_status == 'Rejected'", "source_columns": ["credit_score"], "target_column": "loan_status", "domain": "Finance/Lending", "confidence_score": 0.8, "risk_note": "Thresholds for loan rejection vary greatly by lender and loan product. This is a common heuristic."}, {"type": "range_constraint", "condition": "discharge_date < admission_date", "consequence": "INVALID_DATA_ERROR", "source_columns": ["admission_date", "discharge_date"], "domain": "Healthcare", "confidence_score": 1.0, "risk_note": "Logical impossibility; a discharge date must always be on or after the admission date."}], "generation_order": ["date_of_birth", "admission_date", "trade_date", "country", "credit_score", "price", "quantity", "tax_rate", "currency", "settlement_date", "age_at_admission", "total_price", "loan_status", "tax_amount", "discharge_date"], "generation_order_risk_notes": [{"column": "discharge_date", "risk": "Placed after admission_date to allow for the 'discharge_date >= admission_date' rule to be applied. If a `length_of_stay` calculation were present, this order would be critical.", "confidence_score": 0.95}], "cycles_detected": []}}