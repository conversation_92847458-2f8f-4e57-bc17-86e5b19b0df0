
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, WebSocket, WebSocketDisconnect
from fastapi import BackgroundTasks

from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
import logging
from app.orchestrator.workflow_orchestrator import WorkflowOrchestrator
from app.models.requests import DataGenerationRequest, SessionRequest
from app.models.responses import SessionResponse, StatusResponse
from app.services.session_manager import Session<PERSON>anager
from app.websocket.connection_manager import ConnectionManager
from app.storage.storage_manager import StorageManager
from app.utils.logger import setup_logger
import json
import os
from typing import Dict, Any

# Setup logging
logger = setup_logger(__name__)

# Global instances
session_manager = SessionManager()
connection_manager = ConnectionManager()
storage_manager = StorageManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler"""
    logger.info("Starting FastAPI Multi-Agent Application")
    # Initialize storage
    await storage_manager.initialize()
    yield
    logger.info("Shutting down application")

app = FastAPI(
    title="Multi-Agent Data Generation API",
    description="Industry-grade multi-agent system for data profiling, dependency analysis, and synthetic data generation",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Multi-Agent Data Generation API is running", "status": "healthy"}

@app.post("/api/v1/sessions", response_model=SessionResponse)
async def create_session(request: SessionRequest):
    """Create a new session"""
    try:
        session_id = await session_manager.create_session(
            domain=request.domain,
            user_context=request.user_context
        )
        return SessionResponse(
            session_id=session_id,
            status="created",
            message="Session created successfully"
        )
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create session")

@app.post("/api/v1/sessions/{session_id}/upload-csv")
async def upload_csv(session_id: str, file: UploadFile = File(...)):
    """Upload CSV file for processing"""
    try:
        if not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="Only CSV files are allowed")
        
        # Save uploaded file
        file_path = await storage_manager.save_uploaded_file(session_id, file)
        
        # Update session with file info
        await session_manager.update_session(session_id, {"csv_file_path": file_path})
        
        return {"message": "CSV file uploaded successfully", "file_path": file_path}
    
    except Exception as e:
        logger.error(f"Error uploading CSV: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to upload CSV file")

@app.post("/api/v1/sessions/{session_id}/generate")
async def start_data_generation(session_id: str, request: DataGenerationRequest):
    """Start the data generation workflow"""
    try:
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if not session.get("csv_file_path"):
            raise HTTPException(status_code=400, detail="No CSV file uploaded for this session")
        
        # Initialize orchestrator
        orchestrator = WorkflowOrchestrator(
            session_id=session_id,
            connection_manager=connection_manager,
            storage_manager=storage_manager
        )
        
        # Start workflow asynchronously
        await orchestrator.start_workflow(
            csv_file_path=session["csv_file_path"],
            domain=session["domain"],
            user_context=session["user_context"],
            n_rows=request.n_rows,
            batch_size=request.batch_size
        )
        
        return {"message": "Data generation workflow started", "session_id": session_id}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting data generation: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start data generation")

@app.get("/api/v1/sessions/{session_id}/status", response_model=StatusResponse)
async def get_session_status(session_id: str):
    """Get current session status"""
    try:
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return StatusResponse(
            session_id=session_id,
            status=session.get("status", "unknown"),
            current_step=session.get("current_step", ""),
            progress=session.get("progress", 0),
            message=session.get("message", ""),
            data=session.get("data", {})
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get session status")

@app.get("/api/v1/sessions/{session_id}/results")
async def get_results(session_id: str):
    """Get generated results for a session"""
    try:
        results = await storage_manager.get_session_results(session_id)
        return {"session_id": session_id, "results": results}
    
    except Exception as e:
        logger.error(f"Error getting results: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get results")

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time updates"""
    await connection_manager.connect(websocket, session_id)
    try:
        while True:
            # Keep connection alive and handle any incoming messages
            data = await websocket.receive_text()
            # Process any client messages if needed
            await websocket.send_text(f"Message received for session {session_id}: {data}")
    
    except WebSocketDisconnect:
        connection_manager.disconnect(session_id)
        logger.info(f"WebSocket disconnected for session: {session_id}")

@app.delete("/api/v1/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a session and its associated data"""
    try:
        await session_manager.delete_session(session_id)
        await storage_manager.cleanup_session_data(session_id)
        return {"message": "Session deleted successfully"}
    
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete session")
    
    

from fastapi.responses import FileResponse
from fastapi import Form

from fastapi.responses import FileResponse
import json
import csv
import asyncio

@app.post("/api/v1/run-complete-workflow")
async def run_complete_workflow(
    background_tasks: BackgroundTasks,
    domain: str = Form(...),
    user_context: str = Form(...),  # JSON stringified
    n_rows: int = Form(...),
    batch_size: int = Form(...),
    file: UploadFile = File(...)
):
    # Step 1: Create session
    session_id = await session_manager.create_session(
        domain=domain,
        user_context=user_context  # assumed to be raw JSON string
    )

    print(f"Session created: {session_id}")

    # Step 2: Upload CSV
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="Only CSV files are allowed")

    file_path = await storage_manager.save_uploaded_file(session_id, file)
    await session_manager.update_session(session_id, {"csv_file_path": file_path})

    # Step 3: Trigger orchestrator
    orchestrator = WorkflowOrchestrator(
        session_id=session_id,
        connection_manager=connection_manager,
        storage_manager=storage_manager
    )

    await orchestrator.start_workflow(
        csv_file_path=file_path,
        domain=domain,
        user_context=user_context,  # must parse it for orchestrator
        n_rows=n_rows,
        batch_size=batch_size
    )

    # Step 4: Wait for completion
    while True:
        session = await session_manager.get_session(session_id)
        if session and session.get("status") == "completed":
            break
        await asyncio.sleep(5)

    # Step 5: Convert generated JSON to CSV
    generator_result_path = os.path.join(storage_manager.results_dir, f"{session_id}_generator.json")

    with open(generator_result_path, 'r', encoding='utf-8') as f:
        result = json.load(f)

    generated_data = result.get("generated_data", [])
    if not generated_data:
        raise HTTPException(status_code=500, detail="No generated data found")

    final_csv_path = os.path.join(storage_manager.results_dir, f"{session_id}_final_output.csv")

    fieldnames = list(generated_data[0].keys())
    with open(final_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(generated_data)

    # Step 6: Return CSV file
    # return FileResponse(
    #     final_csv_path,
    #     media_type='text/csv',
    #     filename=f"{session_id}_generated.csv"
    # )
    
    # Step 6: Return CSV file and delete it in background
    background_tasks.add_task(os.unlink, final_csv_path)

    return FileResponse(
        path=final_csv_path,
        media_type='text/csv',
        filename=f"{session_id}_generated.csv",
        headers={
            "Content-Disposition": f"attachment; filename={session_id}_generated.csv"
        },
        background=background_tasks
    )



if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
