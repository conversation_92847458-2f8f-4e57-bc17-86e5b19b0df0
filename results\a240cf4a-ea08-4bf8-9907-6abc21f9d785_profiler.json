{"profile": {"columns": [{"name": "trade_id", "type": "id", "semantic": "Unique identifier for each trade", "constraints": ["not null", "unique"], "depends_on": [], "derived_using": "", "example_values": ["T1001", "T1002", "T1003", "T1004"], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": ["T1001", "T1002", "T1003", "T1004"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "", "profiling_confidence": 1.0, "risk_notes": "", "justification": "Trade ID is a unique identifier as evidenced by unique values in the sample."}, {"name": "portfolio_id", "type": "id", "semantic": "Unique identifier for each portfolio", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": ["P100", "P101", "P102", "P103"], "full_domain": "", "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["P100", "P101", "P102", "P103"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "", "profiling_confidence": 0.9, "risk_notes": "Portfolio IDs may have repeating values, assume multiple trades can belong to one portfolio.", "justification": "Portfolio IDs may have repeating values, indicating multiple trades can belong to one portfolio."}, {"name": "trader_id", "type": "id", "semantic": "Unique identifier for each trader", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": ["TR200", "TR201", "TR202", "TR203"], "full_domain": "", "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["TR200", "TR201", "TR202", "TR203"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "", "profiling_confidence": 0.9, "risk_notes": "Trader IDs may have repeating values, assume multiple trades can be executed by one trader.", "justification": "Trader IDs may have repeating values, indicating multiple trades can be executed by one trader."}, {"name": "counterparty", "type": "categorical", "semantic": "The counterparty involved in the trade", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": ["Goldman Sachs", "<PERSON>", "Morgan Stanley"], "full_domain": ["Goldman Sachs", "<PERSON>", "Morgan Stanley", "Barclays", "Credit Suisse", "HSBC", "BNP Paribas"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["HSBC", "BNP Paribas"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Counterparty values are common financial institutions, additional institutions may be added based on real-world data.", "justification": "Counterparty values are common financial institutions, additional institutions may be added based on real-world data."}, {"name": "instrument_type", "type": "categorical", "semantic": "Type of financial instrument being traded", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": ["Equity", "Bond", "FX"], "full_domain": ["Equity", "Bond", "FX", "Derivative"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["Equity", "FX"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 1.0, "risk_notes": "", "justification": "Instrument type has a small set of distinct values, likely complete based on the sample."}, {"name": "instrument_id", "type": "id", "semantic": "Identifier for the specific financial instrument", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": ["AAPL", "US10Y", "EUR/USD"], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": ["AMZN", "GBP/USD"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.9, "risk_notes": "Instrument IDs have a high cardinality but may change based on domain knowledge. Assume some repeating values like 'AMZN' and 'GBP/USD' are common.", "justification": "Instrument IDs have a high cardinality but some are repeated. These may be tied to the instrument type (e.g., GBP/USD for FX)."}, {"name": "trade_date", "type": "date", "semantic": "Date when the trade was executed", "constraints": ["not null", "must follow YYYY-MM-DD"], "depends_on": [], "derived_using": "", "example_values": ["2023-08-15", "2023-08-16", "2023-08-17"], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": ["2023-08-15", "2023-08-16", "2023-08-17"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "", "justification": "Trade dates are unique and sequential in the sample, but the sample is too small to determine a trend. Assume no specific temporal pattern beyond the sample."}, {"name": "settlement_date", "type": "date", "semantic": "Date when the trade is settled", "constraints": ["not null", "must follow YYYY-MM-DD", "must be greater than or equal to trade_date"], "depends_on": ["trade_date"], "derived_using": "", "example_values": ["2023-08-17", "2023-08-19", "2023-08-21"], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample_constrained", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": "", "justification": "Settlement dates are unique and follow trade dates, with some being the same as the trade date (e.g., FX). Real-world settlement rules may flex depending on instrument type."}, {"name": "quantity", "type": "numeric", "semantic": "Number of units of the instrument being traded", "constraints": ["not null", "must be integer", "must be > 0"], "depends_on": [], "derived_using": "", "example_values": [100, 200, 100000], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "log_normal", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Quantities vary widely depending on the instrument type and size of the trade, assume a log-normal distribution with a long tail.", "justification": "Quantities vary widely by instrument type (e.g., 100,000 for FX vs. 100 for Equity). A log-normal distribution accounts for small and large trades."}, {"name": "price", "type": "numeric", "semantic": "Price per unit of the instrument at the time of the trade", "constraints": ["not null", "must be > 0"], "depends_on": ["instrument_type", "currency"], "derived_using": "", "example_values": [175.5, 99.25, 1.1], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "log_normal", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Prices vary based on the instrument and market conditions. Assume fractional values (decimals) are common. Some instruments (e.g., FX, Bonds) can have very different price scales.", "justification": "Prices are positive and vary based on instrument and market conditions. Typical of financial data, assume log-normally distributed."}, {"name": "currency", "type": "categorical", "semantic": "Currency in which the trade is denominated", "constraints": ["not null"], "depends_on": ["counterparty", "instrument_type"], "derived_using": "", "example_values": ["USD", "EUR", "GBP"], "full_domain": ["USD", "EUR", "GBP"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["USD", "GBP"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Common currencies are present in the sample but additional currencies may exist in real world. Ties to instrument_type and counterparty need verification.", "justification": "Currency values are common (USD, EUR, GBP), likely representing major trading hubs. No evidence of other currencies, so much match common values realistically."}, {"name": "fx_rate_to_usd", "type": "numeric", "semantic": "Exchange rate to convert the trade currency to USD", "constraints": ["not null", "must be ≥ 0"], "depends_on": ["currency"], "derived_using": "", "example_values": [1.0, 1.1, 1.3], "full_domain": "", "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": [1.0, 1.1, 1.3], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.9, "risk_notes": "Sample has only fixed FX rates, assume in real world, they change over time. Currently, 1.0 for USD, but others may vary.", "justification": "FX to USD is 1.0 for USD trades, 1.1 for EUR trades, and 1.3 for GBP trades in the sample. Realistic rates for these currencies at this time period."}, {"name": "trade_value", "type": "numeric", "semantic": "Calculated value of the trade (e.g., quantity × price)", "constraints": ["not null", "must be ≥ 0"], "depends_on": ["quantity", "price"], "derived_using": "trade_value = quantity * price", "example_values": [17550.0, 19850.0, 110000.0], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "formula_based", "temporal_pattern": "", "profiling_confidence": 1.0, "risk_notes": "", "justification": "Trade value is calculated as quantity × price based on the sample. Trade values in the sample follow this formula."}, {"name": "usd_equivalent_value", "type": "numeric", "semantic": "Trade value converted to USD using fx_rate_to_usd", "constraints": ["not null", "must be ≥ 0"], "depends_on": ["trade_value", "fx_rate_to_usd"], "derived_using": "", "example_values": [17550.0, 19850.0, 121000.0], "full_domain": "", "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "formula_based", "temporal_pattern": "", "profiling_confidence": 1.0, "risk_notes": "", "justification": "In the sample, usd_equivalent_value = trade_value × fx_rate_to_usd for non-USD trades, and trade_value for USD trades (which have fx_rate_to_usd = 1.0). Realistic based on market conventions."}, {"name": "buy_sell", "type": "categorical", "semantic": "Direction of the trade (Buy or Sell)", "constraints": ["not null", "value ∈ [Buy, Sell]"], "depends_on": [], "derived_using": "", "example_values": ["Buy", "<PERSON>ll"], "full_domain": ["Buy", "<PERSON>ll"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Buy", "<PERSON>ll"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "", "profiling_confidence": 1.0, "risk_notes": "", "justification": "A binary field with no missing values in the sample."}, {"name": "venue", "type": "categorical", "semantic": "Exchange or platform where the trade was executed", "constraints": ["not null"], "depends_on": ["instrument_type"], "derived_using": "", "example_values": ["NASDAQ", "OTC", "EBS"], "full_domain": ["NASDAQ", "OTC", "EBS", "CME", "LSE", "NYSE"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["LSE", "NYSE"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Venues may be specific to certain instrument types (e.g., NASDAQ for Equities, CME for Derivatives). Assumed complete based on sample.", "justification": "Venues are specific markets/exchanges with expected ties to instrument type (e.g., LSE for GBP trades)."}, {"name": "trade_type", "type": "categorical", "semantic": "Type of order (e.g., Market, Limit)", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": ["Market", "Limit"], "full_domain": ["Market", "Limit"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Market", "Limit"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 1.0, "risk_notes": "", "justification": "Trade type is either Market or Limit in the sample."}, {"name": "status", "type": "categorical", "semantic": "Status of the trade (e.g., Executed, Pending)", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": ["Executed", "Pending"], "full_domain": ["Executed", "Pending", "Rejected"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["Executed", "Pending"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Sample includes 'Executed', 'Pending', and 'Rejected', but additional statuses (e.g., 'Cancelled') may exist in real-world data.", "justification": "Status has a small set of distinct values in the sample. Assume 'Rejected' is a rare but possible status."}, {"name": "risk_score", "type": "numeric", "semantic": "Risk score associated with the trade", "constraints": ["not null", "must be ≥ 0"], "depends_on": [], "derived_using": "", "example_values": [3, 2, 5], "full_domain": [1, 2, 3, 4, 5], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [3, 2, 5], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "", "profiling_confidence": 0.9, "risk_notes": "Assumed to be an integer in [1,5] based on sample. Risk scores may follow more complex rules in real-world data.", "justification": "Risk scores appear to be integers from 1 to 5 in the sample, no other patterns detected."}, {"name": "is_margin_trade", "type": "boolean", "semantic": "Whether the trade was executed on margin (leveraged)", "constraints": ["not null"], "depends_on": [], "derived_using": "", "example_values": [true, false], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [false, true], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 1.0, "risk_notes": "", "justification": "Binary flag with no missing values in the sample. Assume false is more common based on sample, but could vary."}, {"name": "is_compliant", "type": "boolean", "semantic": "Whether the trade complies with regulatory constraints", "constraints": ["not null"], "depends_on": ["risk_score", "is_margin_trade", "trade_value"], "derived_using": "", "example_values": [true, false], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [true, false], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Compliance is often based on risk and regulatory checks. <PERSON><PERSON> shows compliance issues are possible but not too common.", "justification": "is_compliant is mostly true in the sample except when trade is large or high risk (row 10). Assumed to be computed using multiple factors."}, {"name": "compliance_notes", "type": "text", "semantic": "Optional notes explaining compliance decisions", "constraints": [], "depends_on": ["is_compliant", "risk_score", "trade_value"], "derived_using": "", "example_values": ["High leverage", "Exceeds risk threshold"], "full_domain": "", "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["High leverage", "Exceeds risk threshold"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "conditional_text", "temporal_pattern": "", "profiling_confidence": 0.9, "risk_notes": "Compliance notes are only populated when is_compliant = false in the sample. Often tied to risk score/leverage. Additional reasons are likely in real world.", "justification": "Compliance notes are only present when is_compliant is false and usually match some high-risk conditions (leverage, trade size). May involve complex rules not captured in the sample."}, {"name": "approval_status", "type": "categorical", "semantic": "Approval status of the trade (e.g., Approved, Pending, Rejected)", "constraints": ["not null"], "depends_on": ["is_compliant", "status"], "derived_using": "", "example_values": ["Approved", "Pending", "Rejected"], "full_domain": ["Approved", "Pending", "Rejected"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["Approved", "Pending", "Rejected"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "", "profiling_confidence": 0.95, "risk_notes": "Approval status seems aligned with is_compliant and compliance_notes, but may be separate in some cases. Requires further validation.", "justification": "Approval status may be tied to compliance (e.g., rejected for non-compliant trades). In the sample, rejected trades are non-compliant, but not all non-compliant trades are rejected."}], "global_rules": ["IF currency = 'USD' THEN fx_rate_to_usd = 1", "IF fx_rate_to_usd ≠ 1 THEN currency ≠ 'USD'", "IF usd_equivalent_value < trade_value AND fx_rate_to_usd ≤ 1 THEN INVALID", "IF usd_equivalent_value > trade_value AND fx_rate_to_usd ≤ 1 THEN INVALID", "IF is_margin_trade = true THEN risk_score ≥ 3", "IF compliance_notes IS NOT NULL THEN is_compliant = false", "IF is_compliant = false THEN compliance_notes IS NOT NULL", "IF approval_status = 'Rejected' THEN is_compliant = false", "IF instrument_type = 'FX' THEN quantity ≥ 1000", "IF risk_score > 4 THEN is_compliant = false OR approval_status = 'Rejected'"]}, "dataset_info": {"row_count": 15, "column_count": 23, "columns": ["trade_id", "portfolio_id", "trader_id", "counterparty", "instrument_type", "instrument_id", "trade_date", "settlement_date", "quantity", "price", "currency", "fx_rate_to_usd", "trade_value", "usd_equivalent_value", "buy_sell", "venue", "trade_type", "status", "risk_score", "is_margin_trade", "is_compliant", "compliance_notes", "approval_status"]}}