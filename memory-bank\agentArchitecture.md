# DataEcho Agent Architecture

**Last Updated: June 25, 2025**

## Agent System Overview

DataEcho implements a **multi-agent architecture** as its core innovation, moving beyond traditional single-model synthetic data generation to specialized, coordinated AI agents. Each agent has specific expertise and responsibilities within the data generation pipeline.

### Core Design Principles
1. **Specialization**: Each agent focuses on a specific aspect of data generation
2. **Coordination**: Agents work together through orchestrated workflows
3. **Extensibility**: New agents can be added without disrupting existing ones
4. **Transparency**: Each agent's decisions and outputs are traceable
5. **Scalability**: Agent processing can be parallelized for performance

## Current Agent Implementation (MVP1)

### Base Agent Architecture

#### BaseAgent Abstract Class
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from app.services.llm_service import LLMService

class BaseAgent(ABC):
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service
        self.logger = get_logger(self.__class__.__name__)
        self.agent_name = self.__class__.__name__
    
    @abstractmethod
    async def execute(self, input_data: AgentInput) -> AgentOutput:
        """Execute agent-specific logic"""
        pass
    
    def validate_input(self, input_data: AgentInput) -> bool:
        """Validate input data before processing"""
        pass
    
    def handle_error(self, error: Exception) -> AgentError:
        """Standardized error handling"""
        pass
    
    async def log_execution(self, input_data: AgentInput, output: AgentOutput):
        """Log agent execution for transparency"""
        pass
```

### Agent Communication Protocol

#### Current Implementation: File-Based Communication
```python
class AgentCommunicationProtocol:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.session_path = f"sessions/{session_id}"
    
    async def write_result(self, agent_name: str, result: Dict[str, Any]):
        """Write agent result to session storage"""
        file_path = f"{self.session_path}/{agent_name.lower()}_result.json"
        await self.save_json(file_path, result)
    
    async def read_result(self, agent_name: str) -> Dict[str, Any]:
        """Read agent result from session storage"""
        file_path = f"{self.session_path}/{agent_name.lower()}_result.json"
        return await self.load_json(file_path)
```

## Current Agent Ecosystem (3 Agents)

### 1. ProfilerAgent

#### Purpose & Responsibilities
- **Primary Function**: Analyze CSV data structure and patterns
- **Input**: CSV file path, domain context, user requirements
- **Output**: Statistical profile, data types, pattern analysis
- **Processing Time**: ~30-60 seconds for typical files

#### Implementation Details
```python
class ProfilerAgent(BaseAgent):
    async def execute(self, input_data: AgentInput) -> ProfilerOutput:
        # Load and sample data for analysis
        df = pd.read_csv(input_data.file_path)
        sample_data = df.head(100)  # Performance optimization
        
        # Statistical analysis
        statistical_summary = df.describe()
        data_types = df.dtypes.to_dict()
        
        # LLM-powered pattern analysis
        analysis_prompt = self.build_analysis_prompt(
            sample_data, 
            input_data.domain,
            input_data.user_context
        )
        
        llm_response = await self.llm_service.analyze(analysis_prompt)
        
        return ProfilerOutput(
            data_profile=llm_response.profile,
            statistical_summary=statistical_summary,
            data_types=data_types,
            pattern_insights=llm_response.insights
        )
```

#### Key Capabilities
- **Statistical Analysis**: Comprehensive statistical profiling using pandas
- **Pattern Recognition**: LLM-powered pattern identification
- **Domain Context**: Incorporates industry-specific knowledge
- **Data Quality Assessment**: Identifies potential data quality issues

### 2. DependencyAgent

#### Purpose & Responsibilities
- **Primary Function**: Identify relationships and dependencies between data fields
- **Input**: ProfilerAgent results, domain context
- **Output**: Relationship mappings, constraint definitions, dependency graph
- **Processing Time**: ~45-90 seconds depending on complexity