{"dependencies": {"profiled_data": {"column_profiles": {"trade_id": {"data_type": "string", "cardinality": "unique", "patterns": ["TRD_####"]}, "trade_date": {"data_type": "date", "min_date": "2023-01-01", "max_date": "2023-12-31"}, "settlement_date": {"data_type": "date", "min_date": "2023-01-03", "max_date": "2024-01-02"}, "instrument_id": {"data_type": "string", "cardinality": "high", "patterns": ["INST_###"], "categories": ["Equity", "Bond", "FX"]}, "quantity": {"data_type": "integer", "min": 1, "max": 1000}, "price": {"data_type": "float", "min": 10.5, "max": 5000.0}, "currency": {"data_type": "string", "categories": ["USD", "EUR", "GBP", "JPY", "INR"]}, "total_amount": {"data_type": "float", "min": 10.5, "max": 5000000.0}, "country": {"data_type": "string", "categories": ["US", "DE", "GB", "JP", "IN"]}, "status": {"data_type": "string", "categories": ["Pending", "Completed", "Cancelled"]}}, "relationships": [{"source": "trade_date", "target": "settlement_date", "type": "temporal_proximity", "strength": 0.9}, {"source": "quantity", "target": "total_amount", "type": "collinearity"}, {"source": "price", "target": "total_amount", "type": "collinearity"}, {"source": "country", "target": "currency", "type": "conditional_mapping"}], "value_distributions": {"currency": {"USD": 0.4, "EUR": 0.2, "GBP": 0.15, "JPY": 0.15, "INR": 0.1}, "country": {"US": 0.4, "DE": 0.2, "GB": 0.15, "JP": 0.15, "IN": 0.1}}}, "domain_context": {"dataset_name": "FinancialTrades", "description": "Dataset containing mock financial trade records.", "industry": "Finance"}}}