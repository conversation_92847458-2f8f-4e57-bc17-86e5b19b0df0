{"profile": {"columns": [{"name": "trade_id", "type": "id", "semantic": "Unique identifier for each trade", "constraints": ["not null", "unique"], "depends_on": [], "derived_using": null, "example_values": ["T2001", "T2002", "T2003"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "unique_sequential_with_prefix", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": null, "justification": "Every trade has a unique ID starting with 'T' followed by numbers. All values are unique and not null in the sample."}, {"name": "portfolio_id", "type": "id", "semantic": "Identifier for the portfolio associated with the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["P200", "P201", "P202"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["P100", "P101", "P102"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": "Sample size small; limited confidence that observed distribution is fully representative.", "justification": "All values start with 'P' followed by numbers. Not all are unique (one portfolio can have many trades). Distribution appears somewhat skewed to match observed samples."}, {"name": "trader_id", "type": "id", "semantic": "Identifier for the trader who executed the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["TR300", "TR301", "TR302"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["TR200", "TR201", "TR202"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.75, "risk_notes": "Small sample size implies distribution is not fully representative, but pattern of 'TR' + sequential numbers is clear.", "justification": "All trader IDs start with 'TR' followed by numbers. The distribution is skewed and not unique, as one trader may execute multiple trades."}, {"name": "counterparty", "type": "categorical", "semantic": "The institution on the other side of the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Goldman Sachs", "<PERSON>", "Morgan Stanley"], "full_domain": ["Goldman Sachs", "<PERSON>", "Morgan Stanley", "Barclays", "Credit Suisse", "HSBC", "BNP Paribas"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["HSBC", "BNP Paribas", "Goldman Sachs"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.7, "risk_notes": "Sample size is small; additional counterparties may exist beyond what is observed.", "justification": "Values are categorical and match known financial institutions. The full domain is inferred from sample and common financial firms."}, {"name": "instrument_type", "type": "categorical", "semantic": "The type of financial instrument being traded", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Equity", "Bond", "FX"], "full_domain": ["Equity", "Bond", "FX", "Derivative"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Equity", "Bond", "FX"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": "Confidence is high, but other instrument types may exist beyond the sample.", "justification": "Column appears to have clear categorical values. The sample includes 4 types, but others might exist in real-world data."}, {"name": "instrument_id", "type": "id", "semantic": "Identifier for the financial instrument being traded (e.g., stock ticker, bond ID, currency pair)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["AAPL", "US10Y", "EUR/USD"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["AMZN", "GBP/USD", "AAPL"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.8, "risk_notes": "Sample size is small, so cardinality estimate is rough. Format varies by instrument type.", "justification": "Varies by instrument type (e.g., tickers for equities, currency pairs for FX). Not unique as the same instrument can be traded multiple times."}, {"name": "trade_date", "type": "date", "semantic": "Date on which the trade was executed", "constraints": ["not null", "valid YYYY-MM-DD format", "before settlement_date"], "depends_on": ["settlement_date"], "derived_using": null, "example_values": ["2023-01-15", "2023-03-22", "2023-12-05"], "full_domain": null, "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": [], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "sequential_dates_with_randomness", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Dates are in YYYY-MM-DD format and are unique or semi-unique (possibly with some repetitions). Trade date must be before the settlement date."}, {"name": "settlement_date", "type": "date", "semantic": "Date on which the trade is settled", "constraints": ["not null", "valid YYYY-MM-DD format", "after trade_date"], "depends_on": ["trade_date"], "derived_using": null, "example_values": ["2023-01-17", "2023-03-25", "2023-12-08"], "full_domain": null, "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": [], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "sequential_dates_with_randomness", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Dates are in YYYY-MM-DD format and are unique or semi-unique. Settlement date is always after the trade date in the sample, and this is likely a hard constraint."}, {"name": "quantity", "type": "numeric", "semantic": "Number of units (e.g., shares, contracts) traded", "constraints": ["not null", "positive integer"], "depends_on": [], "derived_using": null, "example_values": [100, 200, 150], "full_domain": {"min": 1, "max": 100000}, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed_right"}, "recommended_generation_strategy": "log_normal_distribution", "temporal_pattern": null, "profiling_confidence": 0.95, "risk_notes": "Assumes no negative quantities (short positions would likely use another mechanism in real data).", "justification": "All quantities are positive integers. The sample suggests high cardinality with some repetition. In real data, quantities would likely follow a log-normal distribution (many small trades, few large ones)."}, {"name": "price", "type": "numeric", "semantic": "Price per unit at which the trade was executed", "constraints": ["not null", "positive"], "depends_on": [], "derived_using": null, "example_values": [175.5, 99.25, 2725.0], "full_domain": {"min": 0.0001, "max": 1000000.0}, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed_right"}, "recommended_generation_strategy": "log_normal_distribution", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": "Min/max bounds are assumptions and may not reflect reality for all instrument types.", "justification": "All prices are positive numbers. The range varies widely depending on the instrument (e.g., small for FX rates, large for stock/derivatives)."}, {"name": "currency", "type": "categorical", "semantic": "Currency in which the price is denominated", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["USD", "EUR", "GBP"], "full_domain": ["USD", "EUR", "GBP"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["USD"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": "More currencies might be present in larger samples (e.g., JPY, CHF).", "justification": "Takes a limited set of values (USD, EUR, GBP in sample) and appears to relate to `fx_rate_to_usd` (if USD, rate is 1.0)."}, {"name": "fx_rate_to_usd", "type": "numeric", "semantic": "Exchange rate from the trade currency to USD (used for USD-equivalent calculations)", "constraints": ["not null", "positive"], "depends_on": ["currency"], "derived_using": null, "example_values": [1.0, 1.1, 1.3], "full_domain": {"min": 0.5, "max": 2.0}, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [1.0, 1.1, 1.3], "distribution_shape": "skewed"}, "recommended_generation_strategy": "from_lookup_table", "temporal_pattern": null, "profiling_confidence": 0.8, "risk_notes": "No direct formula with `currency` observed, but high likelihood that `fx_rate_to_usd = 1.0` when `currency = 'USD'`.", "justification": "Only a few distinct values in sample, likely related to `currency`. When `currency` is USD, `fx_rate_to_usd` is 1.0. Values seem static per currency, but in reality, they could vary over time. Assumption: fixed in this sample for simplicity."}, {"name": "trade_value", "type": "numeric", "semantic": "Total monetary value of the trade (quantity × price) in the trade currency", "constraints": ["not null", "positive"], "depends_on": ["quantity", "price"], "derived_using": "quantity * price", "example_values": [17550.0, 19850.0, 408750.0], "full_domain": {"min": 0.01, "max": 1000000000.0}, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed_right"}, "recommended_generation_strategy": "derive_from_parents", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": null, "justification": "In all rows, `trade_value` is exactly `quantity * price`. No null values observed."}, {"name": "usd_equivalent_value", "type": "numeric", "semantic": "Total monetary value of the trade converted to USD (trade_value × fx_rate_to_usd)", "constraints": ["not null", "positive"], "depends_on": ["trade_value", "fx_rate_to_usd"], "derived_using": "trade_value * fx_rate_to_usd when currency != 'USD' else trade_value", "example_values": [17550.0, 19850.0, 121000.0], "full_domain": {"min": 0.01, "max": 1000000000.0}, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed_right"}, "recommended_generation_strategy": "derive_from_parents", "temporal_pattern": null, "profiling_confidence": 0.99, "risk_notes": "Assumption: `fx_rate_to_usd` is used in conversion, but no direct formula exists for this column.", "justification": "`usd_equivalent_value` is the `trade_value` converted to USD using the `fx_rate_to_usd` and seems to derive from `trade_value * fx_rate_to_usd`."}, {"name": "buy_sell", "type": "categorical", "semantic": "Direction of the trade (buy or sell)", "constraints": ["not null", "value in ['Buy', 'Sell']"], "depends_on": [], "derived_using": null, "example_values": ["Buy", "<PERSON>ll"], "full_domain": ["Buy", "<PERSON>ll"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Buy", "<PERSON>ll"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": null, "justification": "Takes one of two values (`Buy` or `Sell`), roughly balanced in the sample."}, {"name": "venue", "type": "categorical", "semantic": "Trading venue where the trade was executed", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["NASDAQ", "OTC", "EBS"], "full_domain": ["NASDAQ", "OTC", "EBS", "CME", "LSE", "NYSE"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["LSE", "NYSE", "NASDAQ"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": "More venues might exist in real data, but sample indicates a small set.", "justification": "Takes categorical values representing different exchanges. Limited to what is in the sample (plus plausible additions like LSE, NYSE)."}, {"name": "trade_type", "type": "categorical", "semantic": "The type of trade (e.g., Market, Limit, Stop)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Market", "Limit"], "full_domain": ["Market", "Limit"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Market", "Limit"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.95, "risk_notes": "Other real-world trade types (e.g., Stop, Iceberg) may exist but are not in the sample.", "justification": "Only `Market` and `Limit` appear in the sample, but other types might exist (`Stop`, etc.)."}, {"name": "status", "type": "categorical", "semantic": "Current status of the trade (e.g., Executed, Pending, Rejected)", "constraints": ["not null"], "depends_on": ["approval_status"], "derived_using": null, "example_values": ["Executed", "Pending", "Rejected"], "full_domain": ["Executed", "Pending", "Rejected"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Executed", "Pending"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.8, "risk_notes": "Further statuses might exist (e.g., Cancelled, Failed).", "justification": "A few distinct categorical values. One `Rejected` trade exists with a related `approval_status`."}, {"name": "risk_score", "type": "numeric", "semantic": "Risk score assigned to the trade (higher is riskier)", "constraints": ["not null", "integer", "value between 1 and 5"], "depends_on": [], "derived_using": null, "example_values": [1, 3, 5], "full_domain": {"min": 1, "max": 5}, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [3, 2, 5], "distribution_shape": "skewed"}, "recommended_generation_strategy": "uniform_sample_with_skew", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": "Assumed range is 1–5, but real risk models could have different scales.", "justification": "All values are integers between 1 and 5. No null values."}, {"name": "is_margin_trade", "type": "boolean", "semantic": "Whether the trade is executed on margin (leveraged)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": [true, false], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [false, true], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": null, "justification": "Column contains `true`/`false` values only. Seems balanced."}, {"name": "is_compliant", "type": "boolean", "semantic": "Whether the trade is compliant with internal regulations", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": [true, false], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [true, false], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": "In sample, compliance violations are rare (2 of 15), but this might not reflect reality.", "justification": "Values are boolean, with `true` being more common in the sample."}, {"name": "compliance_notes", "type": "text", "semantic": "Notes or reasons if the trade fails compliance checks", "constraints": ["can be null"], "depends_on": ["is_compliant"], "derived_using": null, "example_values": ["High exposure", "Exceeds risk threshold", null], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [null], "distribution_shape": "skewed"}, "recommended_generation_strategy": "conditional_on_parents", "temporal_pattern": null, "profiling_confidence": 0.7, "risk_notes": "Possible that not all non-compliant trades have notes (or some compliant ones do have notes).", "justification": "Only contains text when `is_compliant` is `false` in the sample, but this is not guaranteed for all systems."}, {"name": "approval_status", "type": "categorical", "semantic": "Whether the trade is approved or rejected by the risk system", "constraints": ["not null"], "depends_on": ["status"], "derived_using": null, "example_values": ["Approved", "Rejected", "Pending"], "full_domain": ["Approved", "Rejected", "Pending"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Approved", "Pending"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.8, "risk_notes": "Sample suggests conditional relationship with `status` (e.g., when `status` is `Rejected`, `approval_status` is `Rejected`). Possible that `Pending` is a simpler version of `status` values, which might need refinement.", "justification": "Contains `Approved`, `Rejected`, and `Pending`. The `Rejected` value is associated with `status = 'Rejected'` in the data, but it's unclear if they are always 1:1 or if other relationships exist."}], "global_rules": ["IF is_compliant = false THEN compliance_notes IS NOT null", "IF status = 'Rejected' THEN approval_status = 'Rejected'", "IF trade_date >= settlement_date THEN ERROR 'settlement_date must be after trade_date'", "IF fx_rate_to_usd = 1.0 THEN currency = 'USD'", "IF currency = 'USD' THEN fx_rate_to_usd = 1.0", "IF instrument_type = 'Equity' THEN instrument_id IN ['AAPL', 'GOOGL', 'AMZN'] ELSE IF instrument_type = 'FX' THEN instrument_id IN ['EUR/USD', 'GBP/USD']", "IF usd_equivalent_value IS NOT (trade_value * fx_rate_to_usd) THEN ERROR 'usd_equivalent_value is incorrect'"]}, "dataset_info": {"row_count": 15, "column_count": 23, "columns": ["trade_id", "portfolio_id", "trader_id", "counterparty", "instrument_type", "instrument_id", "trade_date", "settlement_date", "quantity", "price", "currency", "fx_rate_to_usd", "trade_value", "usd_equivalent_value", "buy_sell", "venue", "trade_type", "status", "risk_score", "is_margin_trade", "is_compliant", "compliance_notes", "approval_status"]}}