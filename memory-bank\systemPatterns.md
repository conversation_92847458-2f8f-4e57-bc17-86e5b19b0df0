# DataEcho System Patterns & Architecture

**Last Updated: June 25, 2025**

## System Architecture Overview

DataEcho implements a **multi-agent architecture** orchestrated through FastAPI, representing a fundamental innovation in synthetic data generation. Unlike traditional single-model approaches, DataEcho deploys specialized AI agents that work in coordinated sequences and (planned) parallel configurations.

### High-Level Architecture Pattern

```
Input Layer (Multi-Modal)
├── CSV File Upload
├── Natural Language Requirements
├── Screenshot/Image Processing (planned)
└── API Data Sources (planned)
        ↓
FastAPI Orchestration Layer
├── Session Management
├── Workflow Coordination
├── Progress Tracking
└── Error Handling
        ↓
LangGraph Agent Framework (current: basic, planned: advanced)
├── Agent State Management
├── Workflow Orchestration
├── Parallel Processing Coordination
└── Result Aggregation
        ↓
Agent Ecosystem
├── Current: 3 Sequential Agents
├── Planned: 15+ Specialized Agents
└── Future: Custom User-Defined Agents
        ↓
Output Layer
├── CSV Generation
├── JSON Export
├── Quality Reports
├── Trust Scores
└── Explanation Logs
```

## Core Architectural Patterns

### 1. Multi-Agent Coordination Pattern

#### Current Implementation (MVP1)
**Pattern**: Sequential Agent Pipeline
```python
class WorkflowOrchestrator:
    def execute_workflow(self, session_id: str) -> WorkflowResult:
        # Sequential execution pattern
        profile_result = ProfilerAgent().execute(session_data)
        dependency_result = DependencyAgent().execute(profile_result)
        generation_result = GeneratorAgent().execute(dependency_result)
        return WorkflowResult(generation_result)
```

**Characteristics**:
- Simple sequential execution
- File-based inter-agent communication
- Session state persistence in JSON files
- Error handling at orchestrator level

#### Planned Implementation (MVP2)
**Pattern**: LangGraph State Machine Coordination
```python
from langgraph import StateGraph, MessagesState

class DataEchoWorkflow:
    def create_workflow(self) -> StateGraph:
        workflow = StateGraph(DataEchoState)
        
        # Parallel agent coordination
        workflow.add_node("profiler", ProfilerAgent())
        workflow.add_node("dependency", DependencyAgent())
        workflow.add_node("domain_enricher", DomainEnricherAgent())
        
        # Conditional routing based on data characteristics
        workflow.add_conditional_edges("profiler", self.route_based_on_complexity)
        return workflow.compile()
```

### 2. Agent Communication Pattern

#### Current Pattern: File-Based State Sharing
```
Session Storage Pattern:
sessions/{session_id}/
├── session_data.json          # Basic session metadata
├── profiler_result.json       # ProfilerAgent output
├── dependency_result.json     # DependencyAgent output
└── generation_result.json     # GeneratorAgent output
```

#### Planned Pattern: Shared State Graph
```python
class DataEchoState(TypedDict):
    session_id: str
    user_context: str
    domain: str
    data_profile: Dict[str, Any]
    dependencies: List[Dependency]
    generation_config: GenerationConfig
    quality_metrics: QualityMetrics
    progress: ProgressTracker
```

### 3. FastAPI Service Layer Pattern

#### API Architecture Pattern
```python
# Layered architecture with dependency injection
class DataEchoAPI:
    def __init__(self):
        self.session_manager = SessionManager()
        self.workflow_orchestrator = WorkflowOrchestrator()
        self.llm_service = LLMService()
        self.storage_manager = StorageManager()
        
    # RESTful endpoint pattern with async support
    @app.post("/api/v1/sessions")
    async def create_session(self, request: SessionRequest) -> SessionResponse:
        # Session creation with domain context
        
    @app.websocket("/ws/{session_id}")
    async def websocket_endpoint(self, websocket: WebSocket, session_id: str):
        # Real-time progress updates
```

#### Request/Response Pattern
```python
# Structured request/response with Pydantic validation
class SessionRequest(BaseModel):
    domain: str = Field(..., description="Industry domain context")
    user_context: str = Field(..., description="Natural language requirements")
    
class SessionResponse(BaseModel):
    session_id: str
    status: SessionStatus
    message: str
    created_at: datetime
```

### 4. Agent Base Pattern

#### Agent Interface Design
```python
class BaseAgent(ABC):
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service
        self.logger = get_logger(self.__class__.__name__)
    
    @abstractmethod
    async def execute(self, input_data: AgentInput) -> AgentOutput:
        """Execute agent-specific logic with consistent interface"""
        pass
    
    def validate_input(self, input_data: AgentInput) -> bool:
        """Validate input data before processing"""
        pass
    
    def handle_error(self, error: Exception) -> AgentError:
        """Standardized error handling across all agents"""
        pass
```

#### Agent Implementation Pattern
```python
class ProfilerAgent(BaseAgent):
    async def execute(self, input_data: AgentInput) -> ProfilerOutput:
        # Data analysis using pandas + LLM intelligence
        df = pd.read_csv(input_data.file_path)
        sample_data = df.head(100)  # Performance optimization
        
        # LLM-powered analysis with domain context
        analysis_prompt = self.build_analysis_prompt(sample_data, input_data.domain)
        llm_response = await self.llm_service.analyze(analysis_prompt)
        
        return ProfilerOutput(
            data_profile=llm_response.profile,
            statistical_summary=df.describe(),
            data_types=df.dtypes.to_dict()
        )
```

## Design Patterns & Principles

### 1. Domain-Driven Design (DDD)

#### Domain Entities
```python
# Rich domain models with business logic
class SyntheticDataRequest:
    def __init__(self, domain: str, context: str, constraints: List[Constraint]):
        self.domain = Domain(domain)
        self.context = UserContext(context)
        self.constraints = constraints
    
    def validate_for_generation(self) -> ValidationResult:
        # Business rule validation
        pass
    
    def estimate_generation_time(self) -> timedelta:
        # Business logic for time estimation
        pass
```

#### Repository Pattern
```python
class SessionRepository:
    async def save_session(self, session: Session) -> None:
        # Persistence abstraction
        pass
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        # Retrieval abstraction
        pass
```

### 2. Event-Driven Architecture

#### Event Publishing Pattern
```python
class EventPublisher:
    async def publish_agent_started(self, agent_name: str, session_id: str):
        event = AgentStartedEvent(agent_name, session_id, datetime.utcnow())
        await self.websocket_manager.broadcast(session_id, event)
    
    async def publish_generation_progress(self, session_id: str, progress: int):
        event = GenerationProgressEvent(session_id, progress, datetime.utcnow())
        await self.websocket_manager.broadcast(session_id, event)
```

### 3. Strategy Pattern for Generation

#### Generation Strategy Interface
```python
class GenerationStrategy(ABC):
    @abstractmethod
    async def generate_batch(self, config: GenerationConfig) -> List[Dict]:
        pass

class LLMGenerationStrategy(GenerationStrategy):
    async def generate_batch(self, config: GenerationConfig) -> List[Dict]:
        # LLM-based generation with domain intelligence
        pass

class HybridGenerationStrategy(GenerationStrategy):
    async def generate_batch(self, config: GenerationConfig) -> List[Dict]:
        # Combination of statistical + LLM + rule-based generation
        pass
```

## Key Architectural Decisions

### Decision 1: FastAPI over Flask
**Context**: Need for high-performance async API with automatic documentation
**Decision**: FastAPI for async support, automatic OpenAPI docs, type validation
**Consequences**: Better performance, developer experience, but learning curve
**Status**: Implemented and validated

### Decision 2: LangGraph over Traditional LangChain
**Context**: Complex multi-agent coordination requirements
**Decision**: LangGraph for sophisticated state management and workflow orchestration
**Consequences**: More powerful agent coordination, but additional complexity
**Status**: Planned for MVP2, basic implementation in MVP1

### Decision 3: File-Based Persistence (Current) vs Database (Future)
**Context**: MVP1 needs simple, reliable persistence without external dependencies
**Decision**: JSON file storage for MVP1, database migration planned for MVP2/3
**Consequences**: Simple development and deployment, but limited scalability
**Status**: Current implementation, migration planned

### Decision 4: OpenRouter vs Direct LLM APIs
**Context**: Need for cost-effective LLM access during development
**Decision**: OpenRouter for unified API access to multiple models
**Consequences**: Cost efficiency and model flexibility, but additional API dependency
**Status**: Implemented with DeepSeek free tier

### Decision 5: Docker Compose for Development
**Context**: Complex development environment with multiple dependencies
**Decision**: Docker Compose for consistent development environment
**Consequences**: Simplified setup and deployment, but container overhead
**Status**: Implemented and documented

## Component Relationships

### Agent Dependency Graph
```
ProfilerAgent (Independent)
    ↓ provides data_profile
DependencyAgent (depends on ProfilerAgent)
    ↓ provides relationship_mapping
GeneratorAgent (depends on both)
    ↓ produces synthetic_data

Planned Extensions:
WebSearchAgent (parallel to ProfilerAgent)
DomainEnricherAgent (depends on WebSearchAgent)
ValidationAgent (depends on GeneratorAgent)
TrustScoreAgent (depends on ValidationAgent)
```

### Service Dependency Graph
```
FastAPI Application
├── SessionManager (manages session lifecycle)
├── WorkflowOrchestrator (coordinates agents)
│   ├── ProfilerAgent
│   ├── DependencyAgent
│   └── GeneratorAgent
├── LLMService (OpenRouter integration)
├── StorageManager (file system operations)
├── WebSocketManager (real-time updates)
└── ConfigurationManager (environment settings)
```

## Performance Patterns

### 1. Async Processing Pattern
- All I/O operations use async/await
- Background task processing for long-running operations
- WebSocket for real-time progress updates

### 2. Batch Processing Pattern
- Configurable batch sizes for large dataset generation
- Memory management for large file processing
- Progressive result streaming

### 3. Caching Pattern (Planned)
- Agent result caching for similar requests
- LLM response caching for common patterns
- Session state caching for performance

## Security Patterns

### 1. Input Validation Pattern
- Pydantic models for request validation
- File type and size validation
- SQL injection prevention (future database integration)

### 2. Privacy-by-Design Pattern
- No persistent storage of user data beyond session
- Configurable data retention policies
- Audit logging for compliance

### 3. API Security Pattern
- Rate limiting (planned)
- API key authentication (planned)
- Request size limits

---
*These system patterns guide all architectural decisions and implementation approaches for DataEcho.*