{"profile": {"columns": [{"name": "trade_id", "type": "id", "semantic": "Unique identifier for the trade", "constraints": ["not null", "unique"], "depends_on": [], "derived_using": null, "example_values": ["T1001", "T1002", "T1015"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "sequential_or_random_uniform", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`trade_id` is a unique alphanumeric sequence, behaving as a primary key."}, {"name": "portfolio_id", "type": "categorical", "semantic": "Unique identifier for the portfolio executing the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["P100", "P101", "P102"], "full_domain": ["P100", "P101", "P102", "P103", "P104", "...(other possible values)"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["P100", "P101", "P102"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`portfolio_id` is a categorical field with repeating values. New portfolios could arise."}, {"name": "trader_id", "type": "categorical", "semantic": "Unique identifier for the trader executing the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["TR200", "TR201", "TR202"], "full_domain": ["TR200", "TR201", "TR202", "TR203", "TR204", "...(other possible values)"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["TR200", "TR201", "TR202"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`trader_id` is categorical with repeating values across trades."}, {"name": "counterparty", "type": "categorical", "semantic": "Financial institution on the other side of the trade (buyer/seller)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Goldman Sachs", "<PERSON>", "Morgan Stanley"], "full_domain": ["Goldman Sachs", "<PERSON>", "Morgan Stanley", "Barclays", "Credit Suisse", "HSBC", "BNP Paribas", "...(other major financial institutions)"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["HSBC", "BNP Paribas", "<PERSON>"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced_with_rare_cases", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": ["Full domain is inferred from examples and industry knowledge, but new counterparties might exist."], "justification": "The sample contains recognizable financial institutions."}, {"name": "instrument_type", "type": "categorical", "semantic": "Type of financial instrument being traded (Equity, Bond, FX, Derivative, etc.)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Equity", "Bond", "FX"], "full_domain": ["Equity", "Bond", "FX", "Derivative", "...(other instrument types)"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["Equity", "Bond", "FX"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.95, "risk_notes": ["Other instrument types (e.g., options, swaps) could exist but are not in the sample."], "justification": "Sample covers common types, but industry has more possibilities."}, {"name": "instrument_id", "type": "categorical", "semantic": "Unique identifier of the traded financial instrument", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["AAPL", "US10Y", "EUR/USD"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": ["AMZN", "GBP/USD", "AAPL"], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "zipf_distribution_with_edge_cases", "temporal_pattern": null, "profiling_confidence": 0.8, "risk_notes": ["This is an identifier field, but the full domain is unknown (need external reference data)."], "justification": "`instrument_id` identifiers are diverse but likely follow conventions (ticker symbols, ISINs, etc.)."}, {"name": "trade_date", "type": "date", "semantic": "Date when the trade is executed", "constraints": ["not null", "format YYYY-MM-DD"], "depends_on": [], "derived_using": null, "example_values": ["2023-08-15", "2023-08-16", "2023-08-17"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "no_trend"}, "recommended_generation_strategy": "uniform_date_with_jitter", "temporal_pattern": "increasing", "profiling_confidence": 1.0, "risk_notes": [], "justification": "`trade_date` is a date in YYYY-MM-DD format, sequentially increasing in the sample."}, {"name": "settlement_date", "type": "date", "semantic": "Date when the trade settlement occurs", "constraints": ["not null", "format YYYY-MM-DD", "≥ trade_date"], "depends_on": ["trade_date"], "derived_using": null, "example_values": ["2023-08-17", "2023-08-19", "2023-08-17"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "no_trend"}, "recommended_generation_strategy": "trade_date_plus_business_days", "temporal_pattern": null, "profiling_confidence": 0.99, "risk_notes": ["There's a business rule that `settlement_date` must be after or equal to `trade_date`."], "justification": "`settlement_date` is always >= `trade_date` and varies by trade."}, {"name": "quantity", "type": "numeric", "semantic": "Volume or number of units traded", "constraints": ["not null", "> 0"], "depends_on": [], "derived_using": null, "example_values": [100, 200, 150], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [100, 150, 200], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "poisson_distribution_positive_integers", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": ["Assumption is `quantity` is always an integer > 0."], "justification": "`quantity` is a positive integer, with a wide range of possible values."}, {"name": "price", "type": "numeric", "semantic": "Price per unit of the traded instrument", "constraints": ["not null", "> 0"], "depends_on": [], "derived_using": null, "example_values": [175.5, 99.25, 1.1], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "log_normal_distribution_positive", "temporal_pattern": null, "profiling_confidence": 0.95, "risk_notes": [], "justification": "`price` has a long-tail distribution, with most values > 0."}, {"name": "currency", "type": "categorical", "semantic": "Currency used for pricing the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["USD", "EUR", "GBP"], "full_domain": ["USD", "GBP", "EUR", "and other ISO currency codes"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["USD", "GBP", "EUR"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_with_rare_cases", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": ["Many more ISO currencies may exist in the full dataset."], "justification": "Sample uses USD, GBP, EUR, but trades could be in other currencies."}, {"name": "fx_rate_to_usd", "type": "numeric", "semantic": "Exchange rate converting `currency` to USD (1.0 if `currency` is USD)", "constraints": ["not null", "> 0"], "depends_on": ["currency"], "derived_using": null, "example_values": [1.0, 1.1, 1.3], "full_domain": null, "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": [1.0, 1.3], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "dependent_on_currency_with_realistic_rates", "temporal_pattern": null, "profiling_confidence": 0.95, "risk_notes": ["Business logic: if `currency` is `'USD'`, `fx_rate_to_usd` must be `1`."], "justification": "`fx_rate_to_usd` is generally stable per currency, but may fluctuate."}, {"name": "trade_value", "type": "numeric", "semantic": "Total value of the trade (quantity × price) in the specified `currency`", "constraints": ["not null", "> 0"], "depends_on": ["quantity", "price"], "derived_using": "quantity * price", "example_values": [17550.0, 19850.0, 110000.0], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "auto_generated_from_formula", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`trade_value` is derived from `quantity` × `price`."}, {"name": "usd_equivalent_value", "type": "numeric", "semantic": "Total value of the trade in USD (trade_value × fx_rate_to_usd)", "constraints": ["not null", "> 0"], "depends_on": ["trade_value", "fx_rate_to_usd"], "derived_using": "trade_value * fx_rate_to_usd", "example_values": [17550.0, 19850.0, 121000.0], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "auto_generated_from_formula", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`usd_equivalent_value` is derived from `trade_value` × `fx_rate_to_usd`."}, {"name": "buy_sell", "type": "categorical", "semantic": "Trade direction (buy or sell relative to the portfolio)", "constraints": ["not null", "in `Buy`, `Sell`"], "depends_on": [], "derived_using": null, "example_values": ["Buy", "<PERSON>ll"], "full_domain": ["Buy", "<PERSON>ll"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Buy", "<PERSON>ll"], "distribution_shape": "balanced"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`buy_sell` is binary (`Buy` or `Sell`)."}, {"name": "venue", "type": "categorical", "semantic": "Marketplace (exchange or OTC) where the trade occurred", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["NASDAQ", "OTC", "EBS"], "full_domain": ["NASDAQ", "OTC", "EBS", "CME", "LSE", "NYSE", "...(real trading venues)"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["LSE", "NYSE", "NASDAQ"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced_with_rare_cases", "temporal_pattern": null, "profiling_confidence": 0.85, "risk_notes": ["Many possible venues, but the list might not be exhaustive."], "justification": "Sample maps commonly known financial marketplaces (`OTС` is informal)."}, {"name": "trade_type", "type": "categorical", "semantic": "Mechanism for executing the trade (Market or Limit order)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Market", "Limit"], "full_domain": ["Market", "Limit", "Stop", "...(other order types)"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["Market", "Limit"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.8, "risk_notes": ["Other order types might exist (e.g., `Stop`) but aren't in the sample."], "justification": "`trade_type` has recognizable market order types. More are possible."}, {"name": "status", "type": "categorical", "semantic": "Current lifecycle status of the trade (e.g., `Executed`, `Pending`)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Executed", "Pending", "Rejected"], "full_domain": ["Executed", "Pending", "Rejected", "Cancelled", "Expired"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Executed", "Pending", "Rejected"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.8, "risk_notes": ["More statuses (`Cancelled`, `Expired`) could exist but aren't in the sample."], "justification": "<PERSON><PERSON> shows basic statuses, but the full domain is unknown."}, {"name": "risk_score", "type": "numeric", "semantic": "Internal risk rating assigned to the trade (e.g., 1-5 scale)", "constraints": ["not null", "integer", "≥ 1"], "depends_on": [], "derived_using": null, "example_values": [3, 2, 5], "full_domain": [1, 2, 3, 4, 5], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [3, 2, 5], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_discrete", "temporal_pattern": null, "profiling_confidence": 0.99, "risk_notes": ["Assumption: `risk_score` is an integer between 1–5."], "justification": "`risk_score` is a small range (1–5). Repeated values suggest an enumerated set."}, {"name": "is_margin_trade", "type": "boolean", "semantic": "Whether the trade leverages borrowed funds", "constraints": ["not null", "in `True`, `False`"], "depends_on": [], "derived_using": null, "example_values": [false, true], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [false, true], "distribution_shape": "balanced"}, "recommended_generation_strategy": "uniform_boolean_balanced", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`is_margin_trade` is a simple binary flag (T/F)."}, {"name": "is_compliant", "type": "boolean", "semantic": "Whether the trade complies with internal risk policies", "constraints": ["not null", "in `True`, `False`"], "depends_on": [], "derived_using": null, "example_values": [true, false], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [true, false], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_boolean", "temporal_pattern": null, "profiling_confidence": 1.0, "risk_notes": [], "justification": "`is_compliant` is another binary flag, often `true` in the sample."}, {"name": "compliance_notes", "type": "text", "semantic": "If `is_compliant` is false, provides justification for the violation", "constraints": ["null if `is_compliant` is `true`"], "depends_on": ["is_compliant"], "derived_using": null, "example_values": ["", "High leverage", "Exceeds risk threshold"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [""], "distribution_shape": "long_tail"}, "recommended_generation_strategy": "conditional_text_generation", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": ["`compliance_notes` is populated when `is_compliant` is `false`."], "justification": "`compliance_notes` is related to `is_compliant` status."}, {"name": "approval_status", "type": "categorical", "semantic": "Internal approval status of the trade (`Approved` or `Pending`)", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Approved", "Rejected", "Pending"], "full_domain": ["Approved", "Rejected", "Pending"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Approved", "Pending", "Rejected"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": null, "profiling_confidence": 0.9, "risk_notes": ["`approval_status` seems to diverge from `status` (`Executed` trade can be `Pending` in `approval_status`). This might be a separate post-trade approval flow."], "justification": "Sam<PERSON> suggests `approval_status` is `Approved`/`Pending`/`Rejected`."}], "global_rules": ["IF `currency` == 'USD' THEN `fx_rate_to_usd` == 1.0", "IF `is_compliant` == `true` THEN `compliance_notes` is empty", "`settlement_date` >= `trade_date`", "IF `instrument_type` == 'FX' THEN `instrument_id` is a currency pair (e.g., 'EUR/USD')", "IF `instrument_type` == 'Bond' or 'Equity' THEN `instrument_id` is an ISIN or ticker"]}, "dataset_info": {"row_count": 15, "column_count": 23, "columns": ["trade_id", "portfolio_id", "trader_id", "counterparty", "instrument_type", "instrument_id", "trade_date", "settlement_date", "quantity", "price", "currency", "fx_rate_to_usd", "trade_value", "usd_equivalent_value", "buy_sell", "venue", "trade_type", "status", "risk_score", "is_margin_trade", "is_compliant", "compliance_notes", "approval_status"]}}