{"profile": {"columns": [{"name": "trade_id", "type": "id", "semantic": "Unique trade identifier", "constraints": ["not null", "unique"], "depends_on": [], "derived_using": null, "example_values": ["T1001", "T1002", "T1003"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": true, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Unique trade ID based on the trade-specific system."}, {"name": "portfolio_id", "type": "id", "semantic": "Unique portfolio identifier", "constraints": ["not null", "can be reused across trades"], "depends_on": [], "derived_using": null, "example_values": ["P100", "P101", "P102"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["P100", "P101", "P102"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Multiple trades can share the same portfolio ID."}, {"name": "trader_id", "type": "id", "semantic": "Unique identifier of the trader who executed the trade", "constraints": ["not null", "can be reused across trades"], "depends_on": [], "derived_using": null, "example_values": ["TR200", "TR201", "TR202"], "full_domain": null, "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["TR200", "TR201", "TR202"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Multiple trades can be executed by the same trader."}, {"name": "counterparty", "type": "categorical", "semantic": "Institution with which the trade is executed", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["Goldman Sachs", "<PERSON>", "Morgan Stanley"], "full_domain": ["Goldman Sachs", "<PERSON>", "Morgan Stanley", "Barclays", "Credit Suisse", "HSBC", "BNP Paribas"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["HSBC", "BNP Paribas"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Large institutions are commonly used in trade systems."}, {"name": "instrument_type", "type": "categorical", "semantic": "Type of financial instrument", "constraints": ["not null", "value ∈ [Equity, Bond, FX, Derivative]"], "depends_on": [], "derived_using": null, "example_values": ["Equity", "Bond", "FX"], "full_domain": ["Equity", "Bond", "FX", "Derivative"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Equity", "Bond", "FX"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Limited set of instrument types used in trade systems."}, {"name": "instrument_id", "type": "categorical", "semantic": "Identifier of the specific instrument traded", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["AAPL", "US10Y", "EUR/USD"], "full_domain": ["AAPL", "US10Y", "EUR/USD", "SPX500_FUT", "GOOGL", "AMZN", "GBP/USD"], "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": ["AMZN", "GBP/USD"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.9, "risk_notes": "Some instrument IDs seem to be incorrectly assigned (e.g., AMZN as a Bond).", "justification": "Specific instrument IDs are used, but sometimes incorrectly categorized."}, {"name": "trade_date", "type": "date", "semantic": "Date on which the trade was executed", "constraints": ["not null", "must follow YYYY-MM-DD"], "depends_on": [], "derived_using": null, "example_values": ["2023-08-15", "2023-08-16", "2023-08-17"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "increasing", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Trades are executed in a monotonic sequence of dates."}, {"name": "settlement_date", "type": "date", "semantic": "Date on which the trade is settled", "constraints": ["not null", "must follow YYYY-MM-DD", "must be later than trade_date"], "depends_on": ["trade_date"], "derived_using": null, "example_values": ["2023-08-17", "2023-08-19", "2023-08-17"], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "increasing", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Settlement date must be after trade execution date."}, {"name": "quantity", "type": "numeric", "semantic": "Amount of the instrument traded", "constraints": ["not null", "must be > 0"], "depends_on": [], "derived_using": null, "example_values": [100, 200, 100000], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Quantity varies by instrument type and trade type."}, {"name": "price", "type": "numeric", "semantic": "Price per unit of the instrument traded", "constraints": ["not null", "must be > 0"], "depends_on": [], "derived_using": null, "example_values": [175.5, 99.25, 1.1], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Price varies by instrument type and trade type."}, {"name": "currency", "type": "categorical", "semantic": "Currency of the trade", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["USD", "EUR", "GBP"], "full_domain": ["USD", "EUR", "GBP"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["USD", "GBP"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Typical currencies used in financial markets, but there may be logical errors in the data regarding mapping to specific instruments (e.g. AMZN is not likely to be traded in GBP)."}, {"name": "fx_rate_to_usd", "type": "numeric", "semantic": "Foreign exchange rate to USD at the time of trade", "constraints": ["not null", "must be ≥ 0"], "depends_on": [], "derived_using": null, "example_values": [1.0, 1.3, 1.1], "full_domain": null, "distribution": {"cardinality": "medium", "is_unique": false, "most_common_values": [1.0, 1.3], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "no_trend", "profiling_confidence": 0.8, "risk_notes": "FX rates for instruments in USD have confusing defaults (1.0 for USD is correct, but 1.3 for GBP seems off).", "justification": "FX rates are typically applied to non-USD instruments, but sample shows inconsistencies (e.g., 1.3 for GBP/USD is unrealistic)."}, {"name": "trade_value", "type": "numeric", "semantic": "Total value of the trade = quantity × price", "constraints": ["not null", "must be ≥ 0"], "depends_on": ["quantity", "price"], "derived_using": "quantity * price", "example_values": [17550.0, 19850.0, 110000.0], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Validation checks that trade_value = quantity × price."}, {"name": "usd_equivalent_value", "type": "numeric", "semantic": "Value of the trade converted to USD", "constraints": ["not null", "must be ≥ 0"], "depends_on": ["trade_value", "currency", "fx_rate_to_usd"], "derived_using": "IF currency = 'USD' THEN trade_value ELSE trade_value * fx_rate_to_usd", "example_values": [17550.0, 19850.0, 121000.0], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.7, "risk_notes": "USD equivalent calculation seems incorrect: GBP would require dividing the trade value by the FX rate, not multiplying, assuming the FX rate is GBP/USD.", "justification": "USD Equivalent should be using FX Rate appropriately, but sample shows inconsistencies (e.g. GBP × 1.3 is an overestimation)."}, {"name": "buy_sell", "type": "categorical", "semantic": "Direction of the trade", "constraints": ["not null", "value ∈ [Buy, Sell]"], "depends_on": [], "derived_using": null, "example_values": ["Buy", "<PERSON>ll"], "full_domain": ["Buy", "<PERSON>ll"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Buy", "<PERSON>ll"], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Trade can only be Buy or Sell."}, {"name": "venue", "type": "categorical", "semantic": "Exchange or platform where the trade was executed", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": ["NASDAQ", "OTC", "EBS"], "full_domain": ["NASDAQ", "OTC", "EBS", "CME", "LSE", "NYSE"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["NASDAQ", "LSE", "NYSEE"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Some venues are material-specific (e.g. LSE for GBP trades)."}, {"name": "trade_type", "type": "categorical", "semantic": "Type of trade execution (e.g., market, limit)", "constraints": ["not null", "value ∈ [Market, Limit]"], "depends_on": [], "derived_using": null, "example_values": ["Market", "Limit"], "full_domain": ["Market", "Limit"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Market", "Limit"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Trade execution is either market or limit."}, {"name": "status", "type": "categorical", "semantic": "Status of the trade (e.g., Executed, Pending)", "constraints": ["not null", "value ∈ [Executed, Pending, Rejected]"], "depends_on": [], "derived_using": null, "example_values": ["Executed", "Pending", "Rejected"], "full_domain": ["Executed", "Pending", "Rejected"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Executed", "Pending"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Sample shows Executed, Pending, and Rejected statuses."}, {"name": "risk_score", "type": "numeric", "semantic": "Risk assessment score assigned to the trade", "constraints": ["not null", "must be ≥ 1"], "depends_on": [], "derived_using": null, "example_values": [3, 2, 5], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [3, 2, 5], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "no_trend", "profiling_confidence": 1.0, "risk_notes": null, "justification": "Risk scores are ordinal and appear to be assigned arbitrarily."}, {"name": "is_margin_trade", "type": "boolean", "semantic": "Indicates whether the trade involves margin", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": [false, true, false], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [false, true], "distribution_shape": "uniform"}, "recommended_generation_strategy": "uniform_sample", "temporal_pattern": "no_trend", "profiling_confidence": 0.9, "risk_notes": "Margin trades are more common with certain instrument types (e.g., derivatives, FX) but sample is inconsistent.", "justification": "Not all trades are margin trades, but sample uses margin trades more than expected."}, {"name": "is_compliant", "type": "boolean", "semantic": "Indicates whether the trade is compliant with regulations", "constraints": ["not null"], "depends_on": [], "derived_using": null, "example_values": [true, false, true], "full_domain": [true, false], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": [true, false], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.8, "risk_notes": "Margin trades and high risk scores seem to correlate with non-compliance in the sample, but not directly modeled in the data.", "justification": "Most trades are compliant, but some are not due to validation checks."}, {"name": "compliance_notes", "type": "text", "semantic": "Any notes on compliance violations", "constraints": [], "depends_on": ["is_compliant"], "derived_using": "IF is_compliant = false THEN {provide notes} ELSE null", "example_values": ["High Exposure", "Exceeds Risk Threshold", null], "full_domain": null, "distribution": {"cardinality": "high", "is_unique": false, "most_common_values": [null, "High Exposure", "Exceeds Risk Threshold"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.9, "risk_notes": null, "justification": "Notes are sometimes provided for non-compliant trades."}, {"name": "approval_status", "type": "categorical", "semantic": "Approval status of the trade (e.g., Approved, Rejected)", "constraints": ["not null", "value ∈ [Approved, Rejected, Pending]"], "depends_on": [], "derived_using": null, "example_values": ["Approved", "Rejected", "Pending"], "full_domain": ["Approved", "Rejected", "Pending"], "distribution": {"cardinality": "low", "is_unique": false, "most_common_values": ["Approved", "Pending"], "distribution_shape": "skewed"}, "recommended_generation_strategy": "skewed_realistic_balanced", "temporal_pattern": "no_trend", "profiling_confidence": 0.8, "risk_notes": "Some garbage values in sample (e.g., Rejected might correspond with is_compliant=false).", "justification": "Compliance and approval are separate checks, but likely aligned."}], "global_rules": [{"rule": "IF is_compliant = false THEN compliance_notes IS NOT EMPTY", "confidence": 0.8}, {"rule": "IF status = 'Rejected' THEN is_compliant = false", "confidence": 0.9}, {"rule": "IF status = 'Pending' THEN approval_status = 'Pending'", "confidence": 0.9}, {"rule": "IF is_margin_trade = true THEN instrument_type IN (FX, Derivative)", "confidence": 0.5}, {"rule": "IF currency ≠ 'USD' THEN fx_rate_to_usd > 0", "confidence": 0.8}, {"rule": "IF currency = 'USD' THEN fx_rate_to_usd = 1.0", "confidence": 0.8}, {"rule": "IF usd_equivalent_value ≠ trade_value THEN fx_rate_to_usd ≠ 1", "confidence": 0.9}]}, "dataset_info": {"row_count": 15, "column_count": 23, "columns": ["trade_id", "portfolio_id", "trader_id", "counterparty", "instrument_type", "instrument_id", "trade_date", "settlement_date", "quantity", "price", "currency", "fx_rate_to_usd", "trade_value", "usd_equivalent_value", "buy_sell", "venue", "trade_type", "status", "risk_score", "is_margin_trade", "is_compliant", "compliance_notes", "approval_status"]}}