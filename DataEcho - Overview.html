<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataEcho - Technical Architecture & Developer Onboarding</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .mockup-tab { display: none; }
        .mockup-tab.active { display: block; }
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        .agent-flow {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            border-radius: 0.5rem;
            padding: 0.5rem;
            color: white;
            text-align: center;
            margin: 0.25rem;
            min-width: 120px;
        }
        .flow-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 1.5rem;
        }
        .mvp-progress {
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
        }
        .mvp-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #34d399);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">DE</span>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">DataEcho</h1>
                        <p class="text-sm text-gray-600">AI-Powered Synthetic Data Generation Platform</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Publicis Sapient</div>
                    <div class="text-xs text-gray-400">Internal Product Development</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex space-x-8">
                <button onclick="showTab('overview')" class="tab-button py-4 px-1 border-b-2 border-blue-500 text-blue-600 font-medium">
                    <i class="fas fa-home mr-2"></i>Overview
                </button>
                <button onclick="showTab('architecture')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-sitemap mr-2"></i>Architecture
                </button>
                <button onclick="showTab('agents')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-cogs mr-2"></i>Agent Workflows
                </button>
                <button onclick="showTab('implementation')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-code mr-2"></i>Implementation
                </button>
                <button onclick="showTab('competitive')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-chart-bar mr-2"></i>Market Analysis
                </button>
                <button onclick="showTab('ui-mockup')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-desktop mr-2"></i>UI Mockup
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Project Overview</h2>
                
                <!-- Current Status -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-green-50 p-6 rounded-lg">
                        <div class="text-green-600 text-sm font-medium">Current Status</div>
                        <div class="text-2xl font-bold text-green-900">MVP1 Complete</div>
                        <div class="text-sm text-green-700 mt-1">Core agent pipeline operational</div>
                    </div>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <div class="text-blue-600 text-sm font-medium">Market Opportunity</div>
                        <div class="text-2xl font-bold text-blue-900">$8.87B</div>
                        <div class="text-sm text-blue-700 mt-1">Projected market by 2034</div>
                    </div>
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <div class="text-purple-600 text-sm font-medium">Key Differentiator</div>
                        <div class="text-2xl font-bold text-purple-900">Multi-Agent</div>
                        <div class="text-sm text-purple-700 mt-1">LangGraph orchestration</div>
                    </div>
                </div>

                <!-- What is DataEcho -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">What is DataEcho?</h3>
                    <p class="text-gray-700 mb-4">
                        DataEcho is Publicis Sapient's ambitious entry into the synthetic data generation market, designed as a <strong>multi-agent AI system</strong> 
                        that can understand data requirements through natural language, analyze existing patterns, and generate realistic synthetic data 
                        while maintaining privacy compliance and statistical fidelity.
                    </p>
                    <p class="text-gray-700 mb-4">
                        Unlike traditional synthetic data tools that rely on single-model approaches, DataEcho implements a <strong>parallel agent architecture</strong> 
                        where specialized AI agents work simultaneously on different aspects of data generation - from pattern analysis to constraint validation.
                    </p>
                </div>

                <!-- Key Innovation -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Core Innovation: Agent-Based Architecture</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Traditional Approach:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Single model processes entire dataset</li>
                                <li>• Sequential processing bottlenecks</li>
                                <li>• Limited domain awareness</li>
                                <li>• Manual configuration required</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">DataEcho's Multi-Agent System:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Specialized agents for specific tasks</li>
                                <li>• Parallel processing for efficiency</li>
                                <li>• Real-time domain enrichment</li>
                                <li>• Natural language interface</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- MVP Roadmap -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">Development Roadmap</h3>
                    
                    <!-- MVP1 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">MVP1 - Core Foundation</h4>
                            <span class="text-sm font-medium text-green-600">✓ COMPLETED</span>
                        </div>
                        <div class="mvp-progress mb-2">
                            <div class="mvp-fill" style="width: 100%"></div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">Basic agent pipeline, CSV processing, REST API endpoints</div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded">✓ FastAPI Backend</div>
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded">✓ Basic Agents</div>
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded">✓ CSV Import/Export</div>
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded">✓ Statistical Validation</div>
                        </div>
                    </div>

                    <!-- MVP2 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">MVP2 - Enhanced Pipeline</h4>
                            <span class="text-sm font-medium text-orange-600">📋 PLANNED</span>
                        </div>
                        <div class="mvp-progress mb-2">
                            <div class="mvp-fill" style="width: 0%"></div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">LangGraph integration, improved agent coordination, UI foundation</div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">LangGraph Setup</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Agent Coordination</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Basic UI</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Workflow Engine</div>
                        </div>
                    </div>

                    <!-- MVP3 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">MVP3 - Domain Intelligence</h4>
                            <span class="text-sm font-medium text-blue-600">🔮 FUTURE</span>
                        </div>
                        <div class="mvp-progress mb-2">
                            <div class="mvp-fill" style="width: 0%"></div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">RAG integration, domain enrichment, advanced validation</div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">RAG System</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Domain Enrichment</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Advanced UI</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Trust Scoring</div>
                        </div>
                    </div>
                </div>

                <!-- Business Context -->
                <div class="bg-yellow-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Business Context & Market Need</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Market Pain Points:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• $332M annually spent on test data management</li>
                                <li>• 71% of testing delays due to data availability</li>
                                <li>• 92% struggle with privacy compliance</li>
                                <li>• 78% use production data with minimal masking</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">DataEcho's Solution:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Natural language data requirements</li>
                                <li>• Privacy-by-design architecture</li>
                                <li>• Real-time domain enrichment</li>
                                <li>• Automated quality validation</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Architecture Tab -->
        <div id="architecture" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">System Architecture</h2>

                <!-- High-Level Architecture -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">High-Level System Design</h3>
                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <div class="text-center mb-4">
                            <div class="inline-block bg-blue-100 text-blue-800 px-4 py-2 rounded-lg font-medium">Input Sources</div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-file-csv text-green-500 text-2xl mb-2"></i>
                                <div class="font-medium">CSV Files</div>
                                <div class="text-xs text-gray-600">Structured data upload</div>
                            </div>
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-comments text-blue-500 text-2xl mb-2"></i>
                                <div class="font-medium">Natural Language</div>
                                <div class="text-xs text-gray-600">Freeform requirements</div>
                            </div>
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-camera text-purple-500 text-2xl mb-2"></i>
                                <div class="font-medium">Screenshots</div>
                                <div class="text-xs text-gray-600">OCR data extraction</div>
                            </div>
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-database text-orange-500 text-2xl mb-2"></i>
                                <div class="font-medium">Domain Selection</div>
                                <div class="text-xs text-gray-600">Industry templates</div>
                            </div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <i class="fas fa-arrow-down text-gray-400 text-2xl"></i>
                        </div>
                        
                        <div class="text-center mb-4">
                            <div class="inline-block bg-green-100 text-green-800 px-4 py-2 rounded-lg font-medium">FastAPI Orchestration Layer</div>
                        </div>
                        
                        <div class="text-center mb-6">
                            <i class="fas fa-arrow-down text-gray-400 text-2xl"></i>
                        </div>
                        
                        <div class="text-center mb-4">
                            <div class="inline-block bg-purple-100 text-purple-800 px-4 py-2 rounded-lg font-medium">LangGraph Agent Framework</div>
                        </div>
                    </div>
                </div>

                <!-- FastAPI Layer -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">FastAPI Orchestration Layer</h3>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Core Components:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Upload + Trigger + Return:</strong> Handles file uploads and initiates processing pipelines</li>
                                    <li><strong>Seed/Config Manager:</strong> Ensures reproducible data generation through seeding</li>
                                    <li><strong>Session Management:</strong> Maintains state across complex generation workflows</li>
                                    <li><strong>API Gateway:</strong> RESTful endpoints for programmatic access</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Key Features:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Multi-format Support:</strong> CSV, JSON, natural language, images</li>
                                    <li><strong>Async Processing:</strong> Non-blocking operations for large datasets</li>
                                    <li><strong>Configuration Persistence:</strong> Save and replay generation parameters</li>
                                    <li><strong>Error Handling:</strong> Robust error management and recovery</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">API Endpoints (Current MVP1):</h4>
                            <div class="code-block">POST /api/v1/upload          # File upload and initial processing
GET  /api/v1/status/{job_id}   # Check processing status
POST /api/v1/generate         # Trigger data generation
GET  /api/v1/download/{job_id} # Download generated data
POST /api/v1/config           # Save/load configurations</div>
                        </div>
                    </div>
                </div>

                <!-- Agent Framework Architecture -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Multi-Agent Framework</h3>
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The core innovation of DataEcho lies in its multi-agent architecture orchestrated by LangGraph. 
                            Unlike traditional single-model approaches, DataEcho deploys specialized agents that work in parallel 
                            and sequence to handle different aspects of synthetic data generation.
                        </p>
                        
                        <h4 class="font-medium text-gray-900 mb-3">Agent Categories:</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-white p-4 rounded border">
                                <h5 class="font-medium text-blue-600 mb-2">Analysis Agents</h5>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• ProfilerAgent</li>
                                    <li>• StatisticalProfilerAgent</li>
                                    <li>• SchemaInferenceAgent</li>
                                    <li>• DependencyLearnerAgent</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h5 class="font-medium text-green-600 mb-2">Generation Agents</h5>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• SyntheticGeneratorHybrid</li>
                                    <li>• PatternGeneratorAgent</li>
                                    <li>• AnomalyInjector</li>
                                    <li>• TestCaseGeneratorAgent</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h5 class="font-medium text-red-600 mb-2">Validation Agents</h5>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• ValidationAgent</li>
                                    <li>• ConfidenceScorer</li>
                                    <li>• ExplainabilityAgent</li>
                                    <li>• FeedbackAgent</li>
                                </ul>
                            </div>
                        </div>

                        <h4 class="font-medium text-gray-900 mb-3">LangGraph Orchestration Flow:</h4>
                        <div class="bg-white p-4 rounded border">
                            <div class="flex flex-wrap items-center justify-center space-x-2 mb-4">
                                <div class="agent-flow">Entry Point</div>
                                <div class="flow-arrow">→</div>
                                <div class="agent-flow">PromptModelSelectorAgent</div>
                                <div class="flow-arrow">→</div>
                                <div class="agent-flow">ReproducibilityAgent</div>
                                <div class="flow-arrow">→</div>
                                <div class="agent-flow">Agent Pipeline</div>
                            </div>
                            <div class="text-sm text-gray-600 text-center">
                                Initial processing flow before entering the main agent pipeline
                            </div>
                        </div>
                    </div>
                </div>

                <!-- RAG and Enrichment System -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">RAG and Domain Enrichment Layer (MVP3)</h3>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The RAG (Retrieval-Augmented Generation) system provides real-time domain knowledge enrichment, 
                            making DataEcho context-aware and industry-intelligent.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Knowledge Sources:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>WebSearchAgent:</strong> Real-time market data and trends (Tavily/Serper integration)</li>
                                    <li><strong>DomainStandardsFetcher:</strong> Industry standards (ISO, PHR, RBI compliance)</li>
                                    <li><strong>TemporalContextAgent:</strong> Time-aware data patterns and seasonality</li>
                                    <li><strong>LLMSummarizerAgent:</strong> Knowledge synthesis and context integration</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Domain Intelligence:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Finance:</strong> Regulatory compliance, market patterns</li>
                                    <li><strong>Healthcare:</strong> HIPAA compliance, medical terminology</li>
                                    <li><strong>Retail:</strong> Consumer behavior, seasonal trends</li>
                                    <li><strong>Telecommunications:</strong> Usage patterns, geographic data</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Enrichment Pipeline Output:</h4>
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-file-csv text-green-500 mb-1"></i><br>CSV Output
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-code text-blue-500 mb-1"></i><br>JSON Output
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-chart-bar text-purple-500 mb-1"></i><br>Quality Report
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-book-open text-orange-500 mb-1"></i><br>Explanation Log
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-shield-alt text-red-500 mb-1"></i><br>Trust Score
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Processing Pipeline -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Data Processing Pipeline</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The data processing pipeline follows a sophisticated multi-stage approach with parallel and sequential processing capabilities.
                        </p>
                        
                        <div class="space-y-4">
                            <div class="bg-white p-4 rounded border-l-4 border-blue-500">
                                <h5 class="font-medium text-gray-900 mb-2">1. Initial Context & Seed Layer</h5>
                                <p class="text-sm text-gray-700">Establishes reproducible parameters and initial context for the generation process.</p>
                            </div>
                            
                            <div class="bg-white p-4 rounded border-l-4 border-green-500">
                                <h5 class="font-medium text-gray-900 mb-2">2. Multi-File Processing</h5>
                                <p class="text-sm text-gray-700">Handles multiple input files (input_1.csv, input_2.csv, input_3.csv, etc.) with parallel processing capabilities.</p>
                            </div>
                            
                            <div class="bg-white p-4 rounded border-l-4 border-purple-500">
                                <h5 class="font-medium text-gray-900 mb-2">3. Domain Analysis + Knowledge Base Preparation</h5>
                                <p class="text-sm text-gray-700">Analyzes domain context and prepares knowledge base for enrichment.</p>
                            </div>
                            
                            <div class="bg-white p-4 rounded border-l-4 border-orange-500">
                                <h5 class="font-medium text-gray-900 mb-2">4. Profiling Stage</h5>
                                <p class="text-sm text-gray-700">File-wise profiling for each input file, can run parallel or sequential based on configuration.</p>
                            </div>
                            
                            <div class="bg-white p-4 rounded border-l-4 border-red-500">
                                <h5 class="font-medium text-gray-900 mb-2">5. Dependencies Analysis</h5>
                                <p class="text-sm text-gray-700">
                                    Analyzes file-wise internal dependencies, with optional Entity Recognition Agent. 
                                    Can also analyze cross-file dependencies for complex relationships.
                                </p>
                            </div>
                            
                            <div class="bg-white p-4 rounded border-l-4 border-yellow-500">
                                <h5 class="font-medium text-gray-900 mb-2">6. Domain Context Value Enrichment</h5>
                                <p class="text-sm text-gray-700">
                                    Enriches categorical fields and performs file-wise operations to enhance data context and realism.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agents Tab -->
        <div id="agents" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Agent Workflows & Responsibilities</h2>

                <!-- Core Agent Pipeline -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Core Agent Pipeline Flow</h3>
                    <div class="bg-blue-50 p-6 rounded-lg mb-6">
                        <p class="text-gray-700 mb-4">
                            The agent pipeline follows a sophisticated orchestration pattern where agents are activated based on data requirements and processing needs. 
                            The system starts with the Agent Pipeline Entry point and flows through specialized agents.
                        </p>
                        
                        <div class="space-y-4">
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-blue-600 mb-2">Entry and Profiling Phase</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">ProfilerAgent</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Analyzes data structure and schema</li>
                                            <li>• Identifies data types and formats</li>
                                            <li>• Determines column relationships</li>
                                            <li>• Establishes baseline statistics</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">StatisticalProfilerAgent</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Computes statistical distributions</li>
                                            <li>• Identifies outliers and anomalies</li>
                                            <li>• Calculates correlation matrices</li>
                                            <li>• Discovers data patterns</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-green-600 mb-2">Schema and Dependency Analysis</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">SchemaInferenceAgent</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Infers business rules from data</li>
                                            <li>• Identifies primary/foreign keys</li>
                                            <li>• Detects constraint patterns</li>
                                            <li>• Maps entity relationships</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">DependencyLearnerAgent</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Maps functional dependencies</li>
                                            <li>• Discovers conditional relationships</li>
                                            <li>• Identifies hierarchy structures</li>
                                            <li>• Learns business logic patterns</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Specialized Processing Agents -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Specialized Processing Agents</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-purple-50 p-6 rounded-lg">
                            <h4 class="font-medium text-purple-600 mb-3">Knowledge Enhancement</h4>
                            <div class="space-y-3">
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">SymbolicLogicLearner</h5>
                                    <p class="text-sm text-gray-700">Learns symbolic relationships and logical rules from data patterns.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">OntologyEmbedder</h5>
                                    <p class="text-sm text-gray-700">Embeds domain-specific ontologies and taxonomies into the generation process.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">PatternStructureResearcher</h5>
                                    <p class="text-sm text-gray-700">Researches complex patterns and structures within the data domain.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">DomainVocabularyEnricher</h5>
                                    <p class="text-sm text-gray-700">Enriches data with domain-specific vocabulary and terminology.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 p-6 rounded-lg">
                            <h4 class="font-medium text-green-600 mb-3">Generation & Synthesis</h4>
                            <div class="space-y-3">
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">SyntheticGeneratorHybrid</h5>
                                    <p class="text-sm text-gray-700">Core hybrid generation engine combining multiple synthesis approaches.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">PatternGeneratorAgent</h5>
                                    <p class="text-sm text-gray-700">Generates complex patterns based on learned structures and relationships.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">AnomalyInjector</h5>
                                    <p class="text-sm text-gray-700">Intelligently injects realistic anomalies and edge cases.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">AnomalyCorrector</h5>
                                    <p class="text-sm text-gray-700">Corrects and balances anomalies to maintain data realism.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Validation and Quality Assurance -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Validation and Quality Assurance Pipeline</h3>
                    <div class="bg-red-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The validation pipeline ensures generated data meets quality standards and business requirements through multiple specialized agents.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-gray-900 mb-2">SemanticConstraintValidator</h5>
                                    <p class="text-sm text-gray-700 mb-2">Validates semantic constraints and business rules compliance.</p>
                                    <div class="text-xs text-gray-600">
                                        Flows to: ConfidenceScorer
                                    </div>
                                </div>
                                
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-gray-900 mb-2">ValidatorAgent</h5>
                                    <p class="text-sm text-gray-700 mb-2">General validation agent for data integrity and format compliance.</p>
                                    <div class="text-xs text-gray-600">
                                        Flows to: ExplainabilityAgent
                                    </div>
                                </div>
                                
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-gray-900 mb-2">DataQualityAssessor</h5>
                                    <p class="text-sm text-gray-700 mb-2">Assesses overall data quality metrics and completeness.</p>
                                    <div class="text-xs text-gray-600">
                                        Flows to: TestCaseGeneratorAgent
                                    </div>
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-gray-900 mb-2">TrustScoreAndTraceAgent</h5>
                                    <p class="text-sm text-gray-700 mb-2">Generates trust scores and maintains audit trails for generated data.</p>
                                </div>
                                
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-gray-900 mb-2">ConfidenceScorer</h5>
                                    <p class="text-sm text-gray-700 mb-2">Provides confidence metrics for generated data reliability.</p>
                                </div>
                                
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-gray-900 mb-2">ExplainabilityAgent</h5>
                                    <p class="text-sm text-gray-700 mb-2">Generates explanations for generation decisions and data relationships.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 bg-white p-4 rounded border text-center">
                            <h5 class="font-medium text-gray-900 mb-2">AgentPeerReviewer</h5>
                            <p class="text-sm text-gray-700 mb-2">
                                Central coordination agent that aggregates validation results from all quality assurance agents.
                            </p>
                            <div class="text-xs text-gray-600">
                                Final step before FeedbackAgent and output generation
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Agent Features (MVP3+) -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Advanced Agent Features (MVP3+)</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-yellow-50 p-6 rounded-lg">
                            <h4 class="font-medium text-yellow-600 mb-3">
                                <i class="fas fa-shield-alt mr-2"></i>Privacy & Security
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>PrivacyGuardAgent:</strong> Ensures GDPR/CCPA compliance</div>
                                <div><strong>DriftDetector:</strong> Monitors for data drift and bias</div>
                                <div><strong>GoldenDataTracerAgent:</strong> Tracks data lineage</div>
                                <div><strong>AutoFineTuner:</strong> Self-improving generation quality</div>
                            </div>
                        </div>
                        
                        <div class="bg-indigo-50 p-6 rounded-lg">
                            <h4 class="font-medium text-indigo-600 mb-3">
                                <i class="fas fa-brain mr-2"></i>Intelligence Enhancement
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>WebSearchAgent:</strong> Real-time market data integration</div>
                                <div><strong>DomainStandardsFetcher:</strong> Industry compliance rules</div>
                                <div><strong>TemporalContextAgent:</strong> Time-aware patterns</div>
                                <div><strong>LLMSummarizerAgent:</strong> Knowledge synthesis</div>
                            </div>
                        </div>
                        
                        <div class="bg-teal-50 p-6 rounded-lg">
                            <h4 class="font-medium text-teal-600 mb-3">
                                <i class="fas fa-cogs mr-2"></i>Process Optimization
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>FeedbackAgent:</strong> Continuous improvement loop</div>
                                <div><strong>TestCaseGeneratorAgent:</strong> Edge case creation</div>
                                <div><strong>AgentPeerReviewer:</strong> Quality coordination</div>
                                <div><strong>Feedback Generator:</strong> Output optimization</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Communication & Coordination -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Agent Communication & Coordination</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            LangGraph orchestrates agent communication through a sophisticated state management system that enables both parallel and sequential processing.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Parallel Processing Capabilities:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li>• Multiple agents can work on different aspects simultaneously</li>
                                    <li>• File-wise profiling runs in parallel for large datasets</li>
                                    <li>• Dependencies analysis can run parallel or sequential</li>
                                    <li>• Cross-file dependencies handled through coordination</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Sequential Coordination:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li>• Profiling must complete before dependency analysis</li>
                                    <li>• Schema inference depends on profiling results</li>
                                    <li>• Generation waits for all analysis phases</li>
                                    <li>• Validation runs after generation completion</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">State Management:</h4>
                            <div class="code-block">class DataEchoState(TypedDict):
    input_data: Dict[str, Any]
    profiling_results: Dict[str, Any]
    schema_inference: Dict[str, Any]
    dependencies: Dict[str, Any]
    generation_config: Dict[str, Any]
    synthetic_data: Dict[str, Any]
    validation_results: Dict[str, Any]
    trust_scores: Dict[str, float]</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Implementation Tab -->
        <div id="implementation" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Technical Implementation Details</h2>

                <!-- Technology Stack -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Core Technology Stack</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg text-center">
                            <i class="fab fa-python text-blue-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">Python</h4>
                            <p class="text-sm text-gray-600">Core runtime environment</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg text-center">
                            <i class="fas fa-rocket text-green-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">FastAPI</h4>
                            <p class="text-sm text-gray-600">High-performance REST API</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg text-center">
                            <i class="fas fa-project-diagram text-purple-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">LangGraph</h4>
                            <p class="text-sm text-gray-600">Agent orchestration</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg text-center">
                            <i class="fas fa-brain text-orange-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">LLMs</h4>
                            <p class="text-sm text-gray-600">OpenAI/Anthropic models</p>
                        </div>
                    </div>
                </div>

                <!-- Current MVP1 Implementation -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">MVP1 Implementation Status ✅</h3>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Completed Components:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>FastAPI backend with core endpoints</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Basic agent architecture foundation</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>CSV file upload and processing</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Statistical profiling capabilities</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Basic relationship detection</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Simple synthetic data generation</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>JSON/CSV output formats</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Technical Architecture:</h4>
                                <div class="code-block">dataecho/
├── api/
│   ├── endpoints/
│   │   ├── upload.py
│   │   ├── generate.py
│   │   └── download.py
│   └── main.py
├── agents/
│   ├── profiler.py
│   ├── generator.py
│   └── validator.py
├── core/
│   ├── config.py
│   ├── models.py
│   └── utils.py
└── tests/</div>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Key MVP1 APIs:</h4>
                            <div class="code-block"># Upload and process data
POST /api/v1/upload
Content-Type: multipart/form-data
Body: { "file": CSV_FILE }

# Generate synthetic data
POST /api/v1/generate
Content-Type: application/json
Body: {
  "rows": 1000,
  "seed": 42,
  "preserve_relationships": true
}

# Download results
GET /api/v1/download/{job_id}
Response: CSV/JSON synthetic data</div>
                        </div>
                    </div>
                </div>

                <!-- MVP2 Planned Implementation -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">MVP2 Planned Implementation 📋</h3>
                    <div class="bg-orange-50 p-6 rounded-lg">
                        <p class="text-orange-800 mb-4 font-medium">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            MVP2 development has not yet started. This phase focuses on LangGraph integration and enhanced agent coordination.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Planned Features:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>LangGraph workflow integration</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Multi-agent coordination system</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Enhanced dependency learning</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Parallel processing capabilities</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Basic web UI foundation</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Improved validation pipeline</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Technical Challenges:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li>• Agent state management complexity</li>
                                    <li>• Parallel processing coordination</li>
                                    <li>• Error handling across agents</li>
                                    <li>• Performance optimization</li>
                                    <li>• Memory management for large datasets</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Planned LangGraph Implementation:</h4>
                            <div class="code-block">from langgraph import StateGraph, CompiledGraph
from dataecho.agents import ProfilerAgent, DependencyAgent, GeneratorAgent

# Define the workflow graph
workflow = StateGraph(DataEchoState)

# Add agents as nodes
workflow.add_node("profiler", ProfilerAgent())
workflow.add_node("dependency", DependencyAgent())
workflow.add_node("generator", GeneratorAgent())

# Define the flow
workflow.add_edge("profiler", "dependency")
workflow.add_edge("dependency", "generator")

# Compile the graph
app = workflow.compile()</div>
                        </div>
                    </div>
                </div>

                <!-- MVP3 Future Implementation -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">MVP3 Future Implementation 🔮</h3>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <p class="text-blue-800 mb-4">
                            MVP3 represents the full vision of DataEcho with complete RAG integration, domain intelligence, and advanced UI capabilities.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-blue-600 mb-2">RAG Integration</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• WebSearchAgent (Tavily/Serper)</li>
                                    <li>• Domain standards fetching</li>
                                    <li>• Real-time knowledge updates</li>
                                    <li>• Context-aware generation</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-green-600 mb-2">Advanced Agents</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• PrivacyGuardAgent</li>
                                    <li>• ExplainabilityAgent</li>
                                    <li>• TrustScoreAgent</li>
                                    <li>• AutoFineTuner</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-purple-600 mb-2">Professional UI</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• Natural language interface</li>
                                    <li>• Real-time agent monitoring</li>
                                    <li>• Interactive data visualization</li>
                                    <li>• Advanced configuration</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Development Challenges -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Key Development Challenges</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
                            <h4 class="font-medium text-red-800 mb-2">Agent Coordination Complexity</h4>
                            <p class="text-sm text-red-700">
                                Managing state consistency across multiple agents while enabling parallel processing requires sophisticated orchestration patterns.
                            </p>
                        </div>
                        
                        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                            <h4 class="font-medium text-yellow-800 mb-2">Scalability and Performance</h4>
                            <p class="text-sm text-yellow-700">
                                Large datasets and complex relationships can create performance bottlenecks that need careful optimization.
                            </p>
                        </div>
                        
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                            <h4 class="font-medium text-blue-800 mb-2">Domain Knowledge Maintenance</h4>
                            <p class="text-sm text-blue-700">
                                Keeping RAG systems updated with current industry standards and regulations requires ongoing investment.
                            </p>
                        </div>
                        
                        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400">
                            <h4 class="font-medium text-purple-800 mb-2">Quality Assurance</h4>
                            <p class="text-sm text-purple-700">
                                Ensuring generated data maintains statistical fidelity while meeting business constraints across diverse domains.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Architecture Decisions -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Key Architecture Decisions</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Technology Choices:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>FastAPI over Flask:</strong> Better async support and automatic OpenAPI documentation</li>
                                    <li><strong>LangGraph over LangChain:</strong> More sophisticated agent orchestration capabilities</li>
                                    <li><strong>Hybrid Generation:</strong> Combines rule-based, statistical, and AI approaches</li>
                                    <li><strong>Multi-modal Input:</strong> Supports diverse input methods for better UX</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Design Principles:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Privacy by Design:</strong> Built-in compliance and data protection</li>
                                    <li><strong>Explainable AI:</strong> Transparent generation process and decisions</li>
                                    <li><strong>Modular Architecture:</strong> Pluggable agents and components</li>
                                    <li><strong>Enterprise Ready:</strong> Scalable, auditable, and integratable</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Competitive Analysis Tab -->
        <div id="competitive" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Market Analysis & Competitive Landscape</h2>

                <!-- Market Overview -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Market Opportunity</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-gradient-to-r from-green-400 to-blue-500 text-white p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold mb-2">$310.5M</div>
                            <div class="text-sm opacity-90">Current Market Size (2024)</div>
                        </div>
                        <div class="bg-gradient-to-r from-purple-400 to-pink-500 text-white p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold mb-2">$8.87B</div>
                            <div class="text-sm opacity-90">Projected Market (2034)</div>
                        </div>
                        <div class="bg-gradient-to-r from-orange-400 to-red-500 text-white p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold mb-2">35.2%</div>
                            <div class="text-sm opacity-90">Annual Growth Rate (CAGR)</div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 p-6 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-3">Market Pain Points:</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <ul class="space-y-2 text-sm text-gray-700">
                                <li>• <strong>$332M</strong> annually spent on test data management (Gartner, 2023)</li>
                                <li>• <strong>71%</strong> of testing delays due to data availability issues</li>
                            </ul>
                            <ul class="space-y-2 text-sm text-gray-700">
                                <li>• <strong>92%</strong> struggle with data privacy compliance</li>
                                <li>• <strong>78%</strong> use production data with minimal masking</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Competitive Analysis -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Competitive Landscape</h3>
                    
                    <!-- DataEcho vs Competitors Table -->
                    <div class="overflow-x-auto mb-6">
                        <table class="min-w-full bg-white border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Feature</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-green-600 uppercase bg-green-50">DataEcho</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Mostly.ai</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Gretel.ai</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Hazy</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Delphix</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Natural Language Interface</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">Limited</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Multi-Agent Architecture</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Real-time Domain Enrichment</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">Limited</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Multi-modal Input</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV only</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Conversational Data Design</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Context-Aware Generation</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Built-in Trust Scoring</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">Experimental</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Detailed Competitor Analysis -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Detailed Competitor Analysis</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-blue-50 p-6 rounded-lg">
                            <h4 class="font-medium text-blue-600 mb-3">Mostly.ai</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> Privacy-first tabular data generation</div>
                                <div><strong>Strengths:</strong> GDPR compliance, statistical fidelity</div>
                                <div><strong>Limitations:</strong> Tabular only, no natural language interface</div>
                                <div><strong>Pricing:</strong> Flexible pay-per-use model</div>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 p-6 rounded-lg">
                            <h4 class="font-medium text-green-600 mb-3">Gretel.ai</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> API-driven synthetic data platform</div>
                                <div><strong>Strengths:</strong> Multiple data types, robust APIs</div>
                                <div><strong>Limitations:</strong> Traditional ML approach, limited domain awareness</div>
                                <div><strong>Pricing:</strong> Starting at $295/month</div>
                            </div>
                        </div>
                        
                        <div class="bg-purple-50 p-6 rounded-lg">
                            <h4 class="font-medium text-purple-600 mb-3">Hazy</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> Financial services synthetic data</div>
                                <div><strong>Strengths:</strong> Regulatory compliance, industry specialization</div>
                                <div><strong>Limitations:</strong> Narrow industry focus, limited multi-modal</div>
                                <div><strong>Pricing:</strong> Enterprise-focused pricing</div>
                            </div>
                        </div>
                        
                        <div class="bg-orange-50 p-6 rounded-lg">
                            <h4 class="font-medium text-orange-600 mb-3">Delphix</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> Database virtualization and test data management</div>
                                <div><strong>Strengths:</strong> Enterprise infrastructure, data provisioning</div>
                                <div><strong>Limitations:</strong> Virtualization-based, not true synthesis</div>
                                <div><strong>Pricing:</strong> Enterprise licensing model</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- DataEcho's Unique Value Proposition -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">DataEcho's Unique Value Proposition</h3>
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Technical Differentiators:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>First Multi-Agent Architecture:</strong> Parallel specialized intelligence</li>
                                    <li><strong>Natural Language Interface:</strong> Business users can specify requirements</li>
                                    <li><strong>Real-time Domain Enrichment:</strong> Context-aware through RAG systems</li>
                                    <li><strong>Hybrid Generation Approach:</strong> Rules + Statistics + AI</li>
                                    <li><strong>Multi-modal Input Support:</strong> CSV, text, screenshots</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Business Advantages:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Faster Time-to-Value:</strong> Conversational requirements gathering</li>
                                    <li><strong>Reduced Technical Expertise:</strong> Natural language interface</li>
                                    <li><strong>Built-in Explainability:</strong> Transparent generation process</li>
                                    <li><strong>Enterprise-Ready:</strong> Publicis Sapient backing and support</li>
                                    <li><strong>Privacy-by-Design:</strong> Compliance built into architecture</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ROI Analysis -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Return on Investment Analysis</h3>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 mb-2">40-70%</div>
                                <div class="text-sm text-gray-700">Reduction in test data creation time</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 mb-2">15%+</div>
                                <div class="text-sm text-gray-700">Minimum savings in developer time</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 mb-2">85%</div>
                                <div class="text-sm text-gray-700">Reduction in compliance risk</div>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Competitive ROI Advantages:</h4>
                            <ul class="space-y-2 text-sm text-gray-700">
                                <li>• <strong>Faster requirement gathering</strong> through natural language interface</li>
                                <li>• <strong>Reduced need for data science expertise</strong> due to automated agent intelligence</li>
                                <li>• <strong>Lower integration costs</strong> with enterprise-ready architecture</li>
                                <li>• <strong>Reduced compliance risks</strong> through built-in privacy features</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Market Positioning Strategy -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Strategic Market Positioning</h3>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                            <h4 class="font-medium text-blue-800 mb-2">Phase 1: Publicis Sapient Client Pilots</h4>
                            <p class="text-sm text-blue-700">
                                Leverage existing client relationships to validate product-market fit and gather real-world feedback.
                            </p>
                        </div>
                        
                        <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                            <h4 class="font-medium text-green-800 mb-2">Phase 2: Industry-Specific Solutions</h4>
                            <p class="text-sm text-green-700">
                                Develop specialized solutions for finance, healthcare, and retail sectors with domain-specific intelligence.
                            </p>
                        </div>
                        
                        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400">
                            <h4 class="font-medium text-purple-800 mb-2">Phase 3: Horizontal Market Expansion</h4>
                            <p class="text-sm text-purple-700">
                                Expand across industries with generalized synthetic data capabilities and partner ecosystem.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- UI Mockup Tab -->
        <div id="ui-mockup" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">DataEcho UI Mockup</h2>
                <p class="text-gray-600 mb-6">
                    Professional interface mockup showing the envisioned user experience for DataEcho's multi-agent synthetic data generation platform.
                </p>

                <!-- Mockup Navigation -->
                <div class="border-b mb-6">
                    <nav class="flex space-x-8">
                        <button onclick="showMockupTab('dashboard')" class="mockup-tab-button py-2 px-1 border-b-2 border-blue-500 text-blue-600 font-medium">
                            Dashboard
                        </button>
                        <button onclick="showMockupTab('generation')" class="mockup-tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Data Generation
                        </button>
                        <button onclick="showMockupTab('agent-monitor')" class="mockup-tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Agent Monitor
                        </button>
                        <button onclick="showMockupTab('results')" class="mockup-tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Results
                        </button>
                    </nav>
                </div>

                <!-- Dashboard Mockup -->
                <div id="dashboard" class="mockup-tab active">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <!-- Header -->
                        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-red-500 rounded-lg flex items-center justify-center">
                                        <span class="text-white font-bold">DE</span>
                                    </div>
                                    <div>
                                        <h1 class="text-xl font-bold text-gray-900">DataEcho</h1>
                                        <p class="text-sm text-gray-600">AI-Powered Synthetic Data Generation</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="text-sm text-gray-500">Welcome, Developer</div>
                                    <div class="w-8 h-8 bg-gray-300 rounded-full"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-blue-600">12</div>
                                <div class="text-sm text-gray-600">Active Projects</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-green-600">2.3M</div>
                                <div class="text-sm text-gray-600">Records Generated</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-purple-600">94%</div>
                                <div class="text-sm text-gray-600">Quality Score</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-orange-600">8</div>
                                <div class="text-sm text-gray-600">Active Agents</div>
                            </div>
                        </div>

                        <!-- Recent Projects -->
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Projects</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <div>
                                            <div class="font-medium">Customer Demographics - Bank ABC</div>
                                            <div class="text-sm text-gray-600">Generated 100K records • 2 hours ago</div>
                                        </div>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">View</button>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div>
                                            <div class="font-medium">Transaction Data - E-commerce</div>
                                            <div class="text-sm text-gray-600">In progress • 15 min remaining</div>
                                        </div>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">Monitor</button>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        <div>
                                            <div class="font-medium">Healthcare Patient Records</div>
                                            <div class="text-sm text-gray-600">HIPAA compliant • Yesterday</div>
                                        </div>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">Download</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Generation Mockup -->
                <div id="generation" class="mockup-tab">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Create Synthetic Data</h3>
                            
                            <!-- Input Methods -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="border-2 border-blue-500 bg-blue-50 p-4 rounded-lg cursor-pointer">
                                    <div class="text-center">
                                        <i class="fas fa-comments text-blue-500 text-2xl mb-2"></i>
                                        <h4 class="font-medium text-blue-900">Natural Language</h4>
                                        <p class="text-sm text-blue-700">Describe your data needs</p>
                                    </div>
                                </div>
                                <div class="border-2 border-gray-200 p-4 rounded-lg cursor-pointer hover:border-blue-300">
                                    <div class="text-center">
                                        <i class="fas fa-file-csv text-green-500 text-2xl mb-2"></i>
                                        <h4 class="font-medium text-gray-700">Upload CSV</h4>
                                        <p class="text-sm text-gray-600">Use existing data as template</p>
                                    </div>
                                </div>
                                <div class="border-2 border-gray-200 p-4 rounded-lg cursor-pointer hover:border-blue-300">
                                    <div class="text-center">
                                        <i class="fas fa-camera text-purple-500 text-2xl mb-2"></i>
                                        <h4 class="font-medium text-gray-700">Screenshot</h4>
                                        <p class="text-sm text-gray-600">Extract from images</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Natural Language Input -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Describe your data requirements:</label>
                                <textarea class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                         rows="4" 
                                         placeholder="I need customer data for a retail bank with 50,000 records including demographics, account information, transaction history, and risk profiles. The data should be GDPR compliant and include realistic patterns for fraud detection testing."></textarea>
                            </div>

                            <!-- Configuration Options -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of Records</label>
                                    <input type="number" class="w-full p-3 border border-gray-300 rounded-lg" value="50000">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Industry Domain</label>
                                    <select class="w-full p-3 border border-gray-300 rounded-lg">
                                        <option>Financial Services</option>
                                        <option>Healthcare</option>
                                        <option>Retail</option>
                                        <option>Telecommunications</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Compliance</label>
                                    <select class="w-full p-3 border border-gray-300 rounded-lg">
                                        <option>GDPR + CCPA</option>
                                        <option>HIPAA</option>
                                        <option>PCI DSS</option>
                                        <option>Custom</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-4">
                                <button class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium">
                                    <i class="fas fa-magic mr-2"></i>Generate Data
                                </button>
                                <button class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 font-medium">
                                    <i class="fas fa-save mr-2"></i>Save Configuration
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Monitor Mockup -->
                <div id="agent-monitor" class="mockup-tab">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Agent Activity Monitor</h3>
                            
                            <!-- Agent Pipeline Status -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-gray-900">Current Pipeline: Customer Demographics Generation</h4>
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">In Progress</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                </div>
                                <div class="text-sm text-gray-600 mt-1">65% Complete • Estimated 8 minutes remaining</div>
                            </div>

                            <!-- Active Agents -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                            <div>
                                                <div class="font-medium text-green-900">ProfilerAgent</div>
                                                <div class="text-sm text-green-700">Analyzing data structure</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-green-600">Active</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                                            <div>
                                                <div class="font-medium text-blue-900">DependencyLearnerAgent</div>
                                                <div class="text-sm text-blue-700">Mapping relationships</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-blue-600">Active</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-purple-900">WebSearchAgent</div>
                                                <div class="text-sm text-purple-700">Fetching domain data</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-purple-600">Queued</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-gray-700">SyntheticGeneratorHybrid</div>
                                                <div class="text-sm text-gray-600">Waiting for dependencies</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-500">Waiting</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-gray-700">ValidationAgent</div>
                                                <div class="text-sm text-gray-600">Waiting for generation</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-500">Waiting</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-gray-700">TrustScoreAgent</div>
                                                <div class="text-sm text-gray-600">Waiting for validation</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-500">Waiting</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Agent Logs -->
                            <div class="mt-6">
                                <h4 class="font-medium text-gray-900 mb-3">Recent Agent Activity</h4>
                                <div class="bg-gray-100 p-4 rounded-lg font-mono text-sm max-h-32 overflow-y-auto">
                                    <div class="text-green-600">[14:23:15] ProfilerAgent: Started data structure analysis</div>
                                    <div class="text-blue-600">[14:23:18] ProfilerAgent: Detected 15 columns with mixed data types</div>
                                    <div class="text-green-600">[14:23:22] DependencyLearnerAgent: Initiated relationship mapping</div>
                                    <div class="text-blue-600">[14:23:25] DependencyLearnerAgent: Found 8 potential relationships</div>
                                    <div class="text-purple-600">[14:23:28] WebSearchAgent: Queued for banking domain enrichment</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Mockup -->
                <div id="results" class="mockup-tab">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-lg font-semibold text-gray-900">Generation Results</h3>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">✓ Complete</span>
                            </div>

                            <!-- Quality Metrics -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                                <div class="bg-green-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-green-600 mb-1">94%</div>
                                    <div class="text-sm text-gray-600">Overall Quality</div>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-blue-600 mb-1">98%</div>
                                    <div class="text-sm text-gray-600">Statistical Fidelity</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-purple-600 mb-1">92%</div>
                                    <div class="text-sm text-gray-600">Privacy Score</div>
                                </div>
                                <div class="bg-orange-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-orange-600 mb-1">96%</div>
                                    <div class="text-sm text-gray-600">Constraint Compliance</div>
                                </div>
                            </div>

                            <!-- Data Preview -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-gray-900">Data Preview (50,000 records generated)</h4>
                                    <div class="space-x-2">
                                        <button class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                                            <i class="fas fa-download mr-1"></i>Download CSV
                                        </button>
                                        <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded text-sm hover:bg-gray-50">
                                            <i class="fas fa-code mr-1"></i>Export JSON
                                        </button>
                                    </div>
                                </div>
                                <div class="overflow-x-auto border border-gray-200 rounded-lg">
                                    <table class="min-w-full bg-white">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">customer_id</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">name</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">email</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">age</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">balance</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">risk_score</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            <tr>
                                                <td class="px-4 py-2 text-sm text-gray-900">CUST-001847</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">Sarah Johnson</td>
                                                <td class="px-4 py-2 text-sm text-gray-900"><EMAIL></td>
                                                <td class="px-4 py-2 text-sm text-gray-900">34</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">$12,450.00</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">0.23</td>
                                            </tr>
                                            <tr class="bg-gray-50">
                                                <td class="px-4 py-2 text-sm text-gray-900">CUST-002156</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">Michael Chen</td>
                                                <td class="px-4 py-2 text-sm text-gray-900"><EMAIL></td>
                                                <td class="px-4 py-2 text-sm text-gray-900">42</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">$8,900.50</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">0.45</td>
                                            </tr>
                                            <tr>
                                                <td class="px-4 py-2 text-sm text-gray-900">CUST-003291</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">Emma Rodriguez</td>
                                                <td class="px-4 py-2 text-sm text-gray-900"><EMAIL></td>
                                                <td class="px-4 py-2 text-sm text-gray-900">28</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">$5,675.25</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">0.18</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Generation Report -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 mb-3">Generation Report</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <div class="font-medium text-gray-700 mb-2">Execution Summary:</div>
                                        <ul class="space-y-1 text-gray-600">
                                            <li>• Generated 50,000 records in 12 minutes</li>
                                            <li>• Used 8 specialized agents</li>
                                            <li>• Applied GDPR compliance rules</li>
                                            <li>• Maintained 94% statistical fidelity</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700 mb-2">Quality Checks:</div>
                                        <ul class="space-y-1 text-gray-600">
                                            <li>• ✓ All constraints satisfied</li>
                                            <li>• ✓ No sensitive data exposure</li>
                                            <li>• ✓ Relationship integrity maintained</li>
                                            <li>• ✓ Domain standards compliance</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    © 2025 Publicis Sapient. DataEcho - Internal Product Development.
                </div>
                <div class="text-sm text-gray-500">
                    For internal use only. Not ready for external demo.
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab button
            event.target.classList.remove('border-transparent', 'text-gray-500');
            event.target.classList.add('border-blue-500', 'text-blue-600');
        }

        // Mockup tab switching functionality
        function showMockupTab(tabName) {
            // Hide all mockup tab contents
            const mockupTabs = document.querySelectorAll('.mockup-tab');
            mockupTabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all mockup tab buttons
            const mockupButtons = document.querySelectorAll('.mockup-tab-button');
            mockupButtons.forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Show selected mockup tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked mockup tab button
            event.target.classList.remove('border-transparent', 'text-gray-500');
            event.target.classList.add('border-blue-500', 'text-blue-600');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Any initialization code can go here
            console.log('DataEcho Technical Documentation loaded');
        });
    </script>
</body>
</html>
